<template>
  <div class="app-container">
    <header class="header">
      <div class="header-content">
        <h1 class="title">WEBGIS</h1>
        <h2 class="subtitle">DPU BMCK JAWA TENGAH</h2>
      </div>
    </header>

    <main class="main-content">
      <div class="stats-container">
        <StatCard title="Jalan Mantap" :value="'2.185km'" :progress="330" />
        <StatCard title="Product Revenue" value="$16,568" :progress="90" />
        <StatCard title="Product Revenue" value="$16,568" :progress="180" />
      </div>
      <br />
      <div class="map-wrapper">
        <MapComponent />
      </div>
    </main>

    <footer class="footer">
      <div class="footer-content">
        <div class="footer-logo">
          <img src="/logo.png" alt="DPU BINMARCIPA Logo" class="logo-image" />
        </div>
        <div class="footer-info">
          <h3 class="footer-title">DPU BINMARCIPA PROVINSI JAWA TENGAH</h3>
          <div class="footer-details">
            <p class="footer-address">
              <i class="pi pi-map-marker"></i>
              Jl. Madukoro Blok AA-BB Semarang, Jawa Tengah 50144
            </p>
            <p class="footer-contact">
              <i class="pi pi-envelope"></i>
              <EMAIL>
            </p>
            <p class="footer-contact">
              <i class="pi pi-phone"></i>
              (024) 7606710
            </p>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import StatCard from './components/StatCard.vue'
import MapComponent from './components/MapComponent.vue'
</script>

<style>
@import 'tailwindcss';
@import 'tailwindcss-primeui';
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0;
  background: linear-gradient(
    to bottom,
    #e8eaf6 0%,
    #f3e5f5 25%,
    #fce4ec 50%,
    #fff3e0 75%,
    #f5f5f5 100%
  );
  background-attachment: fixed;
  position: relative;
}

.app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(#e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
  pointer-events: none;
}

.header {
  padding: 10rem 0;
  text-align: center;
}

.header-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: 1200px;
  margin: auto;
}

.title {
  font-size: 5rem;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
  color: #7e57c2;
  margin: 0;
  letter-spacing: 2px;
}

.subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: 'Montserrat', sans-serif;
  color: #455a64;
  letter-spacing: 1px;
  margin-top: -10px;
}

.main-content {
  flex: 1;
  /* padding: 0 2rem 2rem; */
  display: flex;
  flex-direction: column;
  gap: 2rem;
  /* max-width: 1200px; */
  margin: 0 auto;
  width: 100%;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  width: 100%;
  max-width: 1200px;
  margin: auto;
  gap: 1.5rem;
}

.map-wrapper {
  flex: 1;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Footer Styles */
.footer {
  background: linear-gradient(135deg, #7e57c2 0%, #5e35b1 100%);
  color: white;
  padding: 2rem 0;
  margin-top: 2rem;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  gap: 2rem;
}

.footer-logo {
  flex-shrink: 0;
}

.logo-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  background: white;
  border-radius: 8px;
  padding: 8px;
}

.footer-info {
  flex: 1;
}

.footer-title {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: 'Montserrat', sans-serif;
  margin: 0 0 1rem 0;
  color: white;
}

.footer-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-address,
.footer-contact {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.9);
}

.footer-address i,
.footer-contact i {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 0 1rem 1rem;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }

  .footer-title {
    font-size: 1.25rem;
  }

  .footer-details {
    align-items: center;
  }
}
</style>
