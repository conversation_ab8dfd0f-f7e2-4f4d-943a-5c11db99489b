<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.3.4/dist/leaflet.css" integrity="sha384-BF7C732iE6WuqJMhUnTNJJLVvW1TIP87P2nMDY7aN2j2EJFWIaqK89j3WlirhFZU" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.3.4/dist/leaflet-src.js" integrity="sha384-oMM5jge511WERo7suehl8Npas4BrPvBEOY54KSP3MygO5gqNxTMKKIcMkS/E9JEX" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../dist/leaflet.markercluster.js"></script>
</head>
<body>

	<div id="map"></div>
	<span>Bug <a href="https://github.com/Leaflet/Leaflet.markercluster/issues/907">#907</a>. Drag a marker from a spiderfied cluster over other clusters.</span><br/>
	<span>Bug <a href="https://github.com/Leaflet/Leaflet.markercluster/issues/808">#808</a>. Drag a marker and while dragging zoom out with scroll-wheel.</span><br/>

	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 12,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(48.85, 2.35);

		var map = new L.Map('map', {center: latlng, zoom: 12, layers: [tiles]});

		var mcg = new L.MarkerClusterGroup();
		map.addLayer(mcg);
	
		var dragMarker;
		var lastCluster;
		var oldZIndex;
		for (var i = 0; i < 25; i += 1) {
			L.marker(getRandomLatLng(), {
				draggable: true
			}).addTo(mcg).on("dragstart", function(event) {
				oldZIndex = event.target._zIndex;
				event.target.setZIndexOffset(-1000);
				dragMarker = this;
			}).on("dragend", function(event) {
				event.target.setZIndexOffset(oldZIndex);
				dragMarker = null;
				if (lastCluster) {
					lastCluster.unspiderfy();
				}
				lastCluster = null
			});
		}
		mcg.on('clustermouseover', function (event) {
			if (dragMarker && lastCluster !== event.layer) {
				if (lastCluster) {
					mcg.once("unspiderfied", function() { // wait for animation end
						event.layer.spiderfy();	
					});
					lastCluster.unspiderfy();
				} else {
					event.layer.spiderfy();
				}
				lastCluster = event.layer;
			}
		});

		function getRandomLatLng() {
			return [
				48.8 + 0.1 * Math.random(),
				2.25 + 0.2 * Math.random()
			];
		}
	</script>
</body>
</html>
