<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">Add 1000 Markers</button><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/51">#51</a>. Click the button. It will add 1000 markers to the map. this should be fast, but previously in (non-IE browsers) it was very slow.</span><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/43">#43</a>. Improving performance more.</span><br/>
	<span id="time"></span>
	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(50.5, 30.51);

		var map = new L.Map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = new L.MarkerClusterGroup();
		var markersList = [];

		function getRandomLatLng(map) {
			var bounds = map.getBounds(),
				southWest = bounds.getSouthWest(),
				northEast = bounds.getNorthEast(),
				lngSpan = northEast.lng - southWest.lng,
				latSpan = northEast.lat - southWest.lat;

			return new L.LatLng(
					southWest.lat + latSpan * Math.random(),
					southWest.lng + lngSpan * Math.random());
		}

		function populate(length) {
			var prestart = +new Date();
			var list = [], i;
			for (i = 0; i < length; i++) {
				var m = new L.Marker(getRandomLatLng(map));
				list.push(m);
			}

			var start = +new Date();

			markers.addLayers(list);

			var end = +new Date();

			document.getElementById('time').innerHTML = 'Creating 1000 markers took: ' + (start - prestart) + 'ms . Appending 1000 markers took: ' + (end - start) + 'ms';

			return false;
		}

		populate(1000);
		var start = +new Date();
		map.addLayer(markers);
		var end = +new Date();
		document.getElementById('time').innerHTML += ' . Adding to map took: ' + (end - start) + 'ms';

		//Ugly add/remove code
		L.DomUtil.get('doit').onclick = function () {
			populate(1000);
		};

	</script>
</body>
</html>
