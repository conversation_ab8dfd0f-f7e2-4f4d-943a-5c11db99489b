<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="screen.css" />

	<link rel="stylesheet" href="../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../dist/MarkerCluster.Default.css" />
	<script src="../dist/leaflet.markercluster-src.js"></script>
	
	<script src="realworld.50000.1.js"></script>
	<script src="realworld.50000.2.js"></script>

</head>
<body>

    <div id="progress"><div id="progress-bar"></div></div>
	<div id="map"></div>
	<span>Mouse over a cluster to see the bounds of its children and click a cluster to zoom to those bounds</span>
	<script type="text/javascript">
		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, Points &copy 2012 LINZ'
			}),
			latlng = L.latLng(-37.79, 175.27);

		var map = L.map('map', { center: latlng, zoom: 13, layers: [tiles] });

		var progress = document.getElementById('progress');
		var progressBar = document.getElementById('progress-bar');

		function updateProgressBar(processed, total, elapsed, layersArray) {
			if (elapsed > 1000) {
				// if it takes more than a second to load, display the progress bar:
				progress.style.display = 'block';
				progressBar.style.width = Math.round(processed/total*100) + '%';
			}

			if (processed === total) {
				// all markers processed - hide the progress bar:
				progress.style.display = 'none';
			}
		}

		var markers = L.markerClusterGroup({ chunkedLoading: true, chunkProgress: updateProgressBar });

		var markerList = [];

		//console.log('start creating markers: ' + window.performance.now());

		for (var i = 0; i < addressPoints.length; i++) {
			var a = addressPoints[i];
			var title = a[2];
			var marker = L.marker(L.latLng(a[0], a[1]), { title: title });
			marker.bindPopup(title);
			markerList.push(marker);
		}
		for (var i = 0; i < addressPoints2.length; i++) {
			var a = addressPoints2[i];
			var title = a[2];
			var marker = L.marker(L.latLng(a[0], a[1]), { title: title });
			marker.bindPopup(title);
			markerList.push(marker);
		}

		//console.log('start clustering: ' + window.performance.now());

		markers.addLayers(markerList);
		map.addLayer(markers);

		//console.log('end clustering: ' + window.performance.now());
	</script>
</body>
</html>
