<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="screen.css" />

	<link rel="stylesheet" href="../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../dist/MarkerCluster.Default.css" />
	<script src="../dist/leaflet.markercluster-src.js"></script>
	<script type="text/javascript" src="geojson-sample.js"></script>

</head>
<body>

	<div id="map"></div>
	<span>Mouse over a cluster to see the bounds of its children and click a cluster to zoom to those bounds</span>
	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = L.latLng(0.78, 102.37);

		var map = L.map('map', {center: latlng, zoom: 7, layers: [tiles]});

		
		var geojson = L.geoJson(geojsonSample, {

			style: function (feature) {
				return {color: feature.properties.color};
			},

			onEachFeature: function (feature, layer) {
				var popupText = 'geometry type: ' + feature.geometry.type;

				if (feature.properties.color) {
					popupText += '<br/>color: ' + feature.properties.color;
				}

				layer.bindPopup(popupText);
			}
		});

		geojson.addLayer(new L.Marker(new L.LatLng(2.745530718801952, 105.194091796875)));

		var eye1 = new L.Marker(new L.LatLng(-0.7250783020332547, 101.8212890625));
		var eye2 = new L.Marker(new L.LatLng(-0.7360637370492077, 103.2275390625));
		var nose = new L.Marker(new L.LatLng(-1.3292264529974207, 102.5463867187));
		var mouth = new L.Polyline([
			new L.LatLng(-1.3841426927920029, 101.7333984375),
			new L.LatLng(-1.6037944300589726, 101.964111328125),
			new L.LatLng(-1.6806671337507222, 102.249755859375),
			new L.LatLng(-1.7355743631421197, 102.67822265625),
			new L.LatLng(-1.5928123762763, 103.0078125),
			new L.LatLng(-1.3292264529974207, 103.3154296875)
		]);
		
		var markers = L.markerClusterGroup();
		markers.addLayer(geojson).addLayers([eye1,eye2,nose,mouth]);
		
		map.addLayer(markers);

	</script>
</body>
</html>
