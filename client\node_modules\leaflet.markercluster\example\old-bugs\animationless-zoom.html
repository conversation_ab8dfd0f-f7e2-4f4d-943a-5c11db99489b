<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">Zoom in</button><br/>
	<span>Bug <a href="https://github.com/leaflet/Leaflet.markercluster/issues/216">#216</a>. Click the button. It will zoom in, leaflet will not do an animation for the zoom. A marker should be visible.</span><br/>
	<span id="time"></span>
	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(-37.36142550190516, 174.254150390625);

		var map = new L.Map('map', {center: latlng, zoom: 7, layers: [tiles]});

		var markers = new L.MarkerClusterGroup();
		markers.addLayer(new L.Marker([-37.77852090603777, 175.3103667497635])); //The one we zoom in on
		markers.addLayer(new L.Marker([-37.711800591811055, 174.50034790039062])); //Marker that we cluster with at the top zoom level, but not 1 level down
		map.addLayer(markers);

		//Ugly add/remove code
		L.DomUtil.get('doit').onclick = function () {
			map.setView([-37.77852090603777, 175.3103667497635], 15);
		};

	</script>
</body>
</html>
