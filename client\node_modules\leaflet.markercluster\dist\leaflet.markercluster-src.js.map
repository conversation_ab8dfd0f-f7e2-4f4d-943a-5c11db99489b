{"version": 3, "file": "leaflet.markercluster-src.js", "sources": ["../src/MarkerClusterGroup.js", "../src/MarkerCluster.js", "../src/MarkerOpacity.js", "../src/DistanceGrid.js", "../src/MarkerCluster.QuickHull.js", "../src/MarkerCluster.Spiderfier.js", "../src/MarkerClusterGroup.Refresh.js"], "sourcesContent": ["/*\n * L.MarkerClusterGroup extends L.FeatureGroup by clustering the markers contained within\n */\n\nexport var MarkerClusterGroup = L.MarkerClusterGroup = L.FeatureGroup.extend({\n\n\toptions: {\n\t\tmaxClusterRadius: 80, //A cluster will cover at most this many pixels from its center\n\t\ticonCreateFunction: null,\n\t\tclusterPane: L.Marker.prototype.options.pane,\n\n\t\tspiderfyOnEveryZoom: false,\n\t\tspiderfyOnMaxZoom: true,\n\t\tshowCoverageOnHover: true,\n\t\tzoomToBoundsOnClick: true,\n\t\tsingleMarkerMode: false,\n\n\t\tdisableClusteringAtZoom: null,\n\n\t\t// Setting this to false prevents the removal of any clusters outside of the viewpoint, which\n\t\t// is the default behaviour for performance reasons.\n\t\tremoveOutsideVisibleBounds: true,\n\n\t\t// Set to false to disable all animations (zoom and spiderfy).\n\t\t// If false, option animateAddingMarkers below has no effect.\n\t\t// If L.DomUtil.TRANSITION is falsy, this option has no effect.\n\t\tanimate: true,\n\n\t\t//Whether to animate adding markers after adding the MarkerClusterGroup to the map\n\t\t// If you are adding individual markers set to true, if adding bulk markers leave false for massive performance gains.\n\t\tanimateAddingMarkers: false,\n\n\t\t// Make it possible to provide custom function to calculate spiderfy shape positions\n\t\tspiderfyShapePositions: null,\n\n\t\t//Increase to increase the distance away that spiderfied markers appear from the center\n\t\tspiderfyDistanceMultiplier: 1,\n\n\t\t// Make it possible to specify a polyline options on a spider leg\n\t\tspiderLegPolylineOptions: { weight: 1.5, color: '#222', opacity: 0.5 },\n\n\t\t// When bulk adding layers, adds markers in chunks. Means addLayers may not add all the layers in the call, others will be loaded during setTimeouts\n\t\tchunkedLoading: false,\n\t\tchunkInterval: 200, // process markers for a maximum of ~ n milliseconds (then trigger the chunkProgress callback)\n\t\tchunkDelay: 50, // at the end of each interval, give n milliseconds back to system/browser\n\t\tchunkProgress: null, // progress callback: function(processed, total, elapsed) (e.g. for a progress indicator)\n\n\t\t//Options to pass to the L.Polygon constructor\n\t\tpolygonOptions: {}\n\t},\n\n\tinitialize: function (options) {\n\t\tL.Util.setOptions(this, options);\n\t\tif (!this.options.iconCreateFunction) {\n\t\t\tthis.options.iconCreateFunction = this._defaultIconCreateFunction;\n\t\t}\n\n\t\tthis._featureGroup = L.featureGroup();\n\t\tthis._featureGroup.addEventParent(this);\n\n\t\tthis._nonPointGroup = L.featureGroup();\n\t\tthis._nonPointGroup.addEventParent(this);\n\n\t\tthis._inZoomAnimation = 0;\n\t\tthis._needsClustering = [];\n\t\tthis._needsRemoving = []; //Markers removed while we aren't on the map need to be kept track of\n\t\t//The bounds of the currently shown area (from _getExpandedVisibleBounds) Updated on zoom/move\n\t\tthis._currentShownBounds = null;\n\n\t\tthis._queue = [];\n\n\t\tthis._childMarkerEventHandlers = {\n\t\t\t'dragstart': this._childMarkerDragStart,\n\t\t\t'move': this._childMarkerMoved,\n\t\t\t'dragend': this._childMarkerDragEnd,\n\t\t};\n\n\t\t// Hook the appropriate animation methods.\n\t\tvar animate = L.DomUtil.TRANSITION && this.options.animate;\n\t\tL.extend(this, animate ? this._withAnimation : this._noAnimation);\n\t\t// Remember which MarkerCluster class to instantiate (animated or not).\n\t\tthis._markerCluster = animate ? L.MarkerCluster : L.MarkerClusterNonAnimated;\n\t},\n\n\taddLayer: function (layer) {\n\n\t\tif (layer instanceof L.LayerGroup) {\n\t\t\treturn this.addLayers([layer]);\n\t\t}\n\n\t\t//Don't cluster non point data\n\t\tif (!layer.getLatLng) {\n\t\t\tthis._nonPointGroup.addLayer(layer);\n\t\t\tthis.fire('layeradd', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!this._map) {\n\t\t\tthis._needsClustering.push(layer);\n\t\t\tthis.fire('layeradd', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this.hasLayer(layer)) {\n\t\t\treturn this;\n\t\t}\n\n\n\t\t//If we have already clustered we'll need to add this one to a cluster\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\t\t}\n\n\t\tthis._addLayer(layer, this._maxZoom);\n\t\tthis.fire('layeradd', { layer: layer });\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\t//Work out what is visible\n\t\tvar visibleLayer = layer,\n\t\t    currentZoom = this._zoom;\n\t\tif (layer.__parent) {\n\t\t\twhile (visibleLayer.__parent._zoom >= currentZoom) {\n\t\t\t\tvisibleLayer = visibleLayer.__parent;\n\t\t\t}\n\t\t}\n\n\t\tif (this._currentShownBounds.contains(visibleLayer.getLatLng())) {\n\t\t\tif (this.options.animateAddingMarkers) {\n\t\t\t\tthis._animationAddLayer(layer, visibleLayer);\n\t\t\t} else {\n\t\t\t\tthis._animationAddLayerNonAnimated(layer, visibleLayer);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\tremoveLayer: function (layer) {\n\n\t\tif (layer instanceof L.LayerGroup) {\n\t\t\treturn this.removeLayers([layer]);\n\t\t}\n\n\t\t//Non point layers\n\t\tif (!layer.getLatLng) {\n\t\t\tthis._nonPointGroup.removeLayer(layer);\n\t\t\tthis.fire('layerremove', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!this._map) {\n\t\t\tif (!this._arraySplice(this._needsClustering, layer) && this.hasLayer(layer)) {\n\t\t\t\tthis._needsRemoving.push({ layer: layer, latlng: layer._latlng });\n\t\t\t}\n\t\t\tthis.fire('layerremove', { layer: layer });\n\t\t\treturn this;\n\t\t}\n\n\t\tif (!layer.__parent) {\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\t\t\tthis._unspiderfyLayer(layer);\n\t\t}\n\n\t\t//Remove the marker from clusters\n\t\tthis._removeLayer(layer, true);\n\t\tthis.fire('layerremove', { layer: layer });\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\tlayer.off(this._childMarkerEventHandlers, this);\n\n\t\tif (this._featureGroup.hasLayer(layer)) {\n\t\t\tthis._featureGroup.removeLayer(layer);\n\t\t\tif (layer.clusterShow) {\n\t\t\t\tlayer.clusterShow();\n\t\t\t}\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t//Takes an array of markers and adds them in bulk\n\taddLayers: function (layersArray, skipLayerAddEvent) {\n\t\tif (!L.Util.isArray(layersArray)) {\n\t\t\treturn this.addLayer(layersArray);\n\t\t}\n\n\t\tvar fg = this._featureGroup,\n\t\t    npg = this._nonPointGroup,\n\t\t    chunked = this.options.chunkedLoading,\n\t\t    chunkInterval = this.options.chunkInterval,\n\t\t    chunkProgress = this.options.chunkProgress,\n\t\t    l = layersArray.length,\n\t\t    offset = 0,\n\t\t    originalArray = true,\n\t\t    m;\n\n\t\tif (this._map) {\n\t\t\tvar started = (new Date()).getTime();\n\t\t\tvar process = L.bind(function () {\n\t\t\t\tvar start = (new Date()).getTime();\n\n\t\t\t\t// Make sure to unspiderfy before starting to add some layers\n\t\t\t\tif (this._map && this._unspiderfy) {\n\t\t\t\t\tthis._unspiderfy();\n\t\t\t\t}\n\n\t\t\t\tfor (; offset < l; offset++) {\n\t\t\t\t\tif (chunked && offset % 200 === 0) {\n\t\t\t\t\t\t// every couple hundred markers, instrument the time elapsed since processing started:\n\t\t\t\t\t\tvar elapsed = (new Date()).getTime() - start;\n\t\t\t\t\t\tif (elapsed > chunkInterval) {\n\t\t\t\t\t\t\tbreak; // been working too hard, time to take a break :-)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tm = layersArray[offset];\n\n\t\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\t\t// Side effects:\n\t\t\t\t\t// - Total increases, so chunkProgress ratio jumps backward.\n\t\t\t\t\t// - Groups are not included in this group, only their non-group child layers (hasLayer).\n\t\t\t\t\t// Changing array length while looping does not affect performance in current browsers:\n\t\t\t\t\t// http://jsperf.com/for-loop-changing-length/6\n\t\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\t//Not point data, can't be clustered\n\t\t\t\t\tif (!m.getLatLng) {\n\t\t\t\t\t\tnpg.addLayer(m);\n\t\t\t\t\t\tif (!skipLayerAddEvent) {\n\t\t\t\t\t\t\tthis.fire('layeradd', { layer: m });\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tthis._addLayer(m, this._maxZoom);\n\t\t\t\t\tif (!skipLayerAddEvent) {\n\t\t\t\t\t\tthis.fire('layeradd', { layer: m });\n\t\t\t\t\t}\n\n\t\t\t\t\t//If we just made a cluster of size 2 then we need to remove the other marker from the map (if it is) or we never will\n\t\t\t\t\tif (m.__parent) {\n\t\t\t\t\t\tif (m.__parent.getChildCount() === 2) {\n\t\t\t\t\t\t\tvar markers = m.__parent.getAllChildMarkers(),\n\t\t\t\t\t\t\t    otherMarker = markers[0] === m ? markers[1] : markers[0];\n\t\t\t\t\t\t\tfg.removeLayer(otherMarker);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (chunkProgress) {\n\t\t\t\t\t// report progress and time elapsed:\n\t\t\t\t\tchunkProgress(offset, l, (new Date()).getTime() - started);\n\t\t\t\t}\n\n\t\t\t\t// Completed processing all markers.\n\t\t\t\tif (offset === l) {\n\n\t\t\t\t\t// Refresh bounds and weighted positions.\n\t\t\t\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\t\t\t\tthis._refreshClustersIcons();\n\n\t\t\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, this._zoom, this._currentShownBounds);\n\t\t\t\t} else {\n\t\t\t\t\tsetTimeout(process, this.options.chunkDelay);\n\t\t\t\t}\n\t\t\t}, this);\n\n\t\t\tprocess();\n\t\t} else {\n\t\t\tvar needsClustering = this._needsClustering;\n\n\t\t\tfor (; offset < l; offset++) {\n\t\t\t\tm = layersArray[offset];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t//Not point data, can't be clustered\n\t\t\t\tif (!m.getLatLng) {\n\t\t\t\t\tnpg.addLayer(m);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tneedsClustering.push(m);\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t},\n\n\t//Takes an array of markers and removes them in bulk\n\tremoveLayers: function (layersArray) {\n\t\tvar i, m,\n\t\t    l = layersArray.length,\n\t\t    fg = this._featureGroup,\n\t\t    npg = this._nonPointGroup,\n\t\t    originalArray = true;\n\n\t\tif (!this._map) {\n\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\tm = layersArray[i];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tif (originalArray) {\n\t\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\t\toriginalArray = false;\n\t\t\t\t\t}\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\t\tl = layersArray.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tthis._arraySplice(this._needsClustering, m);\n\t\t\t\tnpg.removeLayer(m);\n\t\t\t\tif (this.hasLayer(m)) {\n\t\t\t\t\tthis._needsRemoving.push({ layer: m, latlng: m._latlng });\n\t\t\t\t}\n\t\t\t\tthis.fire('layerremove', { layer: m });\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\n\t\tif (this._unspiderfy) {\n\t\t\tthis._unspiderfy();\n\n\t\t\t// Work on a copy of the array, so that next loop is not affected.\n\t\t\tvar layersArray2 = layersArray.slice(),\n\t\t\t    l2 = l;\n\t\t\tfor (i = 0; i < l2; i++) {\n\t\t\t\tm = layersArray2[i];\n\n\t\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\t\tthis._extractNonGroupLayers(m, layersArray2);\n\t\t\t\t\tl2 = layersArray2.length;\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tthis._unspiderfyLayer(m);\n\t\t\t}\n\t\t}\n\n\t\tfor (i = 0; i < l; i++) {\n\t\t\tm = layersArray[i];\n\n\t\t\t// Group of layers, append children to layersArray and skip.\n\t\t\tif (m instanceof L.LayerGroup) {\n\t\t\t\tif (originalArray) {\n\t\t\t\t\tlayersArray = layersArray.slice();\n\t\t\t\t\toriginalArray = false;\n\t\t\t\t}\n\t\t\t\tthis._extractNonGroupLayers(m, layersArray);\n\t\t\t\tl = layersArray.length;\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (!m.__parent) {\n\t\t\t\tnpg.removeLayer(m);\n\t\t\t\tthis.fire('layerremove', { layer: m });\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tthis._removeLayer(m, true, true);\n\t\t\tthis.fire('layerremove', { layer: m });\n\n\t\t\tif (fg.hasLayer(m)) {\n\t\t\t\tfg.removeLayer(m);\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// Refresh bounds and weighted positions.\n\t\tthis._topClusterLevel._recalculateBounds();\n\n\t\tthis._refreshClustersIcons();\n\n\t\t//Fix up the clusters and markers on the map\n\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, this._zoom, this._currentShownBounds);\n\n\t\treturn this;\n\t},\n\n\t//Removes all layers from the MarkerClusterGroup\n\tclearLayers: function () {\n\t\t//Need our own special implementation as the LayerGroup one doesn't work for us\n\n\t\t//If we aren't on the map (yet), blow away the markers we know of\n\t\tif (!this._map) {\n\t\t\tthis._needsClustering = [];\n\t\t\tthis._needsRemoving = [];\n\t\t\tdelete this._gridClusters;\n\t\t\tdelete this._gridUnclustered;\n\t\t}\n\n\t\tif (this._noanimationUnspiderfy) {\n\t\t\tthis._noanimationUnspiderfy();\n\t\t}\n\n\t\t//Remove all the visible layers\n\t\tthis._featureGroup.clearLayers();\n\t\tthis._nonPointGroup.clearLayers();\n\n\t\tthis.eachLayer(function (marker) {\n\t\t\tmarker.off(this._childMarkerEventHandlers, this);\n\t\t\tdelete marker.__parent;\n\t\t}, this);\n\n\t\tif (this._map) {\n\t\t\t//Reset _topClusterLevel and the DistanceGrids\n\t\t\tthis._generateInitialClusters();\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t//Override FeatureGroup.getBounds as it doesn't work\n\tgetBounds: function () {\n\t\tvar bounds = new L.LatLngBounds();\n\n\t\tif (this._topClusterLevel) {\n\t\t\tbounds.extend(this._topClusterLevel._bounds);\n\t\t}\n\n\t\tfor (var i = this._needsClustering.length - 1; i >= 0; i--) {\n\t\t\tbounds.extend(this._needsClustering[i].getLatLng());\n\t\t}\n\n\t\tbounds.extend(this._nonPointGroup.getBounds());\n\n\t\treturn bounds;\n\t},\n\n\t//Overrides LayerGroup.eachLayer\n\teachLayer: function (method, context) {\n\t\tvar markers = this._needsClustering.slice(),\n\t\t\tneedsRemoving = this._needsRemoving,\n\t\t\tthisNeedsRemoving, i, j;\n\n\t\tif (this._topClusterLevel) {\n\t\t\tthis._topClusterLevel.getAllChildMarkers(markers);\n\t\t}\n\n\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\tthisNeedsRemoving = true;\n\n\t\t\tfor (j = needsRemoving.length - 1; j >= 0; j--) {\n\t\t\t\tif (needsRemoving[j].layer === markers[i]) {\n\t\t\t\t\tthisNeedsRemoving = false;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (thisNeedsRemoving) {\n\t\t\t\tmethod.call(context, markers[i]);\n\t\t\t}\n\t\t}\n\n\t\tthis._nonPointGroup.eachLayer(method, context);\n\t},\n\n\t//Overrides LayerGroup.getLayers\n\tgetLayers: function () {\n\t\tvar layers = [];\n\t\tthis.eachLayer(function (l) {\n\t\t\tlayers.push(l);\n\t\t});\n\t\treturn layers;\n\t},\n\n\t//Overrides LayerGroup.getLayer, WARNING: Really bad performance\n\tgetLayer: function (id) {\n\t\tvar result = null;\n\n\t\tid = parseInt(id, 10);\n\n\t\tthis.eachLayer(function (l) {\n\t\t\tif (L.stamp(l) === id) {\n\t\t\t\tresult = l;\n\t\t\t}\n\t\t});\n\n\t\treturn result;\n\t},\n\n\t//Returns true if the given layer is in this MarkerClusterGroup\n\thasLayer: function (layer) {\n\t\tif (!layer) {\n\t\t\treturn false;\n\t\t}\n\n\t\tvar i, anArray = this._needsClustering;\n\n\t\tfor (i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i] === layer) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\tanArray = this._needsRemoving;\n\t\tfor (i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i].layer === layer) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn !!(layer.__parent && layer.__parent._group === this) || this._nonPointGroup.hasLayer(layer);\n\t},\n\n\t//Zoom down to show the given layer (spiderfying if necessary) then calls the callback\n\tzoomToShowLayer: function (layer, callback) {\n\n\t\tvar map = this._map;\n\n\t\tif (typeof callback !== 'function') {\n\t\t\tcallback = function () {};\n\t\t}\n\n\t\tvar showMarker = function () {\n\t\t\t// Assumes that map.hasLayer checks for direct appearance on map, not recursively calling\n\t\t\t// hasLayer on Layer Groups that are on map (typically not calling this MarkerClusterGroup.hasLayer, which would always return true)\n\t\t\tif ((map.hasLayer(layer) || map.hasLayer(layer.__parent)) && !this._inZoomAnimation) {\n\t\t\t\tthis._map.off('moveend', showMarker, this);\n\t\t\t\tthis.off('animationend', showMarker, this);\n\n\t\t\t\tif (map.hasLayer(layer)) {\n\t\t\t\t\tcallback();\n\t\t\t\t} else if (layer.__parent._icon) {\n\t\t\t\t\tthis.once('spiderfied', callback, this);\n\t\t\t\t\tlayer.__parent.spiderfy();\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tif (layer._icon && this._map.getBounds().contains(layer.getLatLng())) {\n\t\t\t//Layer is visible ond on screen, immediate return\n\t\t\tcallback();\n\t\t} else if (layer.__parent._zoom < Math.round(this._map._zoom)) {\n\t\t\t//Layer should be visible at this zoom level. It must not be on screen so just pan over to it\n\t\t\tthis._map.on('moveend', showMarker, this);\n\t\t\tthis._map.panTo(layer.getLatLng());\n\t\t} else {\n\t\t\tthis._map.on('moveend', showMarker, this);\n\t\t\tthis.on('animationend', showMarker, this);\n\t\t\tlayer.__parent.zoomToBounds();\n\t\t}\n\t},\n\n\t//Overrides FeatureGroup.onAdd\n\tonAdd: function (map) {\n\t\tthis._map = map;\n\t\tvar i, l, layer;\n\n\t\tif (!isFinite(this._map.getMaxZoom())) {\n\t\t\tthrow \"Map has no maxZoom specified\";\n\t\t}\n\n\t\tthis._featureGroup.addTo(map);\n\t\tthis._nonPointGroup.addTo(map);\n\n\t\tif (!this._gridClusters) {\n\t\t\tthis._generateInitialClusters();\n\t\t}\n\n\t\tthis._maxLat = map.options.crs.projection.MAX_LATITUDE;\n\n\t\t//Restore all the positions as they are in the MCG before removing them\n\t\tfor (i = 0, l = this._needsRemoving.length; i < l; i++) {\n\t\t\tlayer = this._needsRemoving[i];\n\t\t\tlayer.newlatlng = layer.layer._latlng;\n\t\t\tlayer.layer._latlng = layer.latlng;\n\t\t}\n\t\t//Remove them, then restore their new positions\n\t\tfor (i = 0, l = this._needsRemoving.length; i < l; i++) {\n\t\t\tlayer = this._needsRemoving[i];\n\t\t\tthis._removeLayer(layer.layer, true);\n\t\t\tlayer.layer._latlng = layer.newlatlng;\n\t\t}\n\t\tthis._needsRemoving = [];\n\n\t\t//Remember the current zoom level and bounds\n\t\tthis._zoom = Math.round(this._map._zoom);\n\t\tthis._currentShownBounds = this._getExpandedVisibleBounds();\n\n\t\tthis._map.on('zoomend', this._zoomEnd, this);\n\t\tthis._map.on('moveend', this._moveEnd, this);\n\n\t\tif (this._spiderfierOnAdd) { //TODO FIXME: Not sure how to have spiderfier add something on here nicely\n\t\t\tthis._spiderfierOnAdd();\n\t\t}\n\n\t\tthis._bindEvents();\n\n\t\t//Actually add our markers to the map:\n\t\tl = this._needsClustering;\n\t\tthis._needsClustering = [];\n\t\tthis.addLayers(l, true);\n\t},\n\n\t//Overrides FeatureGroup.onRemove\n\tonRemove: function (map) {\n\t\tmap.off('zoomend', this._zoomEnd, this);\n\t\tmap.off('moveend', this._moveEnd, this);\n\n\t\tthis._unbindEvents();\n\n\t\t//In case we are in a cluster animation\n\t\tthis._map._mapPane.className = this._map._mapPane.className.replace(' leaflet-cluster-anim', '');\n\n\t\tif (this._spiderfierOnRemove) { //TODO FIXME: Not sure how to have spiderfier add something on here nicely\n\t\t\tthis._spiderfierOnRemove();\n\t\t}\n\n\t\tdelete this._maxLat;\n\n\t\t//Clean up all the layers we added to the map\n\t\tthis._hideCoverage();\n\t\tthis._featureGroup.remove();\n\t\tthis._nonPointGroup.remove();\n\n\t\tthis._featureGroup.clearLayers();\n\n\t\tthis._map = null;\n\t},\n\n\tgetVisibleParent: function (marker) {\n\t\tvar vMarker = marker;\n\t\twhile (vMarker && !vMarker._icon) {\n\t\t\tvMarker = vMarker.__parent;\n\t\t}\n\t\treturn vMarker || null;\n\t},\n\n\t//Remove the given object from the given array\n\t_arraySplice: function (anArray, obj) {\n\t\tfor (var i = anArray.length - 1; i >= 0; i--) {\n\t\t\tif (anArray[i] === obj) {\n\t\t\t\tanArray.splice(i, 1);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t},\n\n\t/**\n\t * Removes a marker from all _gridUnclustered zoom levels, starting at the supplied zoom.\n\t * @param marker to be removed from _gridUnclustered.\n\t * @param z integer bottom start zoom level (included)\n\t * @private\n\t */\n\t_removeFromGridUnclustered: function (marker, z) {\n\t\tvar map = this._map,\n\t\t    gridUnclustered = this._gridUnclustered,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\tfor (; z >= minZoom; z--) {\n\t\t\tif (!gridUnclustered[z].removeObject(marker, map.project(marker.getLatLng(), z))) {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t},\n\n\t_childMarkerDragStart: function (e) {\n\t\te.target.__dragStart = e.target._latlng;\n\t},\n\n\t_childMarkerMoved: function (e) {\n\t\tif (!this._ignoreMove && !e.target.__dragStart) {\n\t\t\tvar isPopupOpen = e.target._popup && e.target._popup.isOpen();\n\n\t\t\tthis._moveChild(e.target, e.oldLatLng, e.latlng);\n\n\t\t\tif (isPopupOpen) {\n\t\t\t\te.target.openPopup();\n\t\t\t}\n\t\t}\n\t},\n\n\t_moveChild: function (layer, from, to) {\n\t\tlayer._latlng = from;\n\t\tthis.removeLayer(layer);\n\n\t\tlayer._latlng = to;\n\t\tthis.addLayer(layer);\n\t},\n\n\t_childMarkerDragEnd: function (e) {\n\t\tvar dragStart = e.target.__dragStart;\n\t\tdelete e.target.__dragStart;\n\t\tif (dragStart) {\n\t\t\tthis._moveChild(e.target, dragStart, e.target._latlng);\n\t\t}\t\t\n\t},\n\n\n\t//Internal function for removing a marker from everything.\n\t//dontUpdateMap: set to true if you will handle updating the map manually (for bulk functions)\n\t_removeLayer: function (marker, removeFromDistanceGrid, dontUpdateMap) {\n\t\tvar gridClusters = this._gridClusters,\n\t\t\tgridUnclustered = this._gridUnclustered,\n\t\t\tfg = this._featureGroup,\n\t\t\tmap = this._map,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\t//Remove the marker from distance clusters it might be in\n\t\tif (removeFromDistanceGrid) {\n\t\t\tthis._removeFromGridUnclustered(marker, this._maxZoom);\n\t\t}\n\n\t\t//Work our way up the clusters removing them as we go if required\n\t\tvar cluster = marker.__parent,\n\t\t\tmarkers = cluster._markers,\n\t\t\totherMarker;\n\n\t\t//Remove the marker from the immediate parents marker list\n\t\tthis._arraySplice(markers, marker);\n\n\t\twhile (cluster) {\n\t\t\tcluster._childCount--;\n\t\t\tcluster._boundsNeedUpdate = true;\n\n\t\t\tif (cluster._zoom < minZoom) {\n\t\t\t\t//Top level, do nothing\n\t\t\t\tbreak;\n\t\t\t} else if (removeFromDistanceGrid && cluster._childCount <= 1) { //Cluster no longer required\n\t\t\t\t//We need to push the other marker up to the parent\n\t\t\t\totherMarker = cluster._markers[0] === marker ? cluster._markers[1] : cluster._markers[0];\n\n\t\t\t\t//Update distance grid\n\t\t\t\tgridClusters[cluster._zoom].removeObject(cluster, map.project(cluster._cLatLng, cluster._zoom));\n\t\t\t\tgridUnclustered[cluster._zoom].addObject(otherMarker, map.project(otherMarker.getLatLng(), cluster._zoom));\n\n\t\t\t\t//Move otherMarker up to parent\n\t\t\t\tthis._arraySplice(cluster.__parent._childClusters, cluster);\n\t\t\t\tcluster.__parent._markers.push(otherMarker);\n\t\t\t\totherMarker.__parent = cluster.__parent;\n\n\t\t\t\tif (cluster._icon) {\n\t\t\t\t\t//Cluster is currently on the map, need to put the marker on the map instead\n\t\t\t\t\tfg.removeLayer(cluster);\n\t\t\t\t\tif (!dontUpdateMap) {\n\t\t\t\t\t\tfg.addLayer(otherMarker);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcluster._iconNeedsUpdate = true;\n\t\t\t}\n\n\t\t\tcluster = cluster.__parent;\n\t\t}\n\n\t\tdelete marker.__parent;\n\t},\n\n\t_isOrIsParent: function (el, oel) {\n\t\twhile (oel) {\n\t\t\tif (el === oel) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\toel = oel.parentNode;\n\t\t}\n\t\treturn false;\n\t},\n\n\t//Override L.Evented.fire\n\tfire: function (type, data, propagate) {\n\t\tif (data && data.layer instanceof L.MarkerCluster) {\n\t\t\t//Prevent multiple clustermouseover/off events if the icon is made up of stacked divs (Doesn't work in ie <= 8, no relatedTarget)\n\t\t\tif (data.originalEvent && this._isOrIsParent(data.layer._icon, data.originalEvent.relatedTarget)) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\ttype = 'cluster' + type;\n\t\t}\n\n\t\tL.FeatureGroup.prototype.fire.call(this, type, data, propagate);\n\t},\n\n\t//Override L.Evented.listens\n\tlistens: function (type, propagate) {\n\t\treturn L.FeatureGroup.prototype.listens.call(this, type, propagate) || L.FeatureGroup.prototype.listens.call(this, 'cluster' + type, propagate);\n\t},\n\n\t//Default functionality\n\t_defaultIconCreateFunction: function (cluster) {\n\t\tvar childCount = cluster.getChildCount();\n\n\t\tvar c = ' marker-cluster-';\n\t\tif (childCount < 10) {\n\t\t\tc += 'small';\n\t\t} else if (childCount < 100) {\n\t\t\tc += 'medium';\n\t\t} else {\n\t\t\tc += 'large';\n\t\t}\n\n\t\treturn new L.DivIcon({ html: '<div><span>' + childCount + '</span></div>', className: 'marker-cluster' + c, iconSize: new L.Point(40, 40) });\n\t},\n\n\t_bindEvents: function () {\n\t\tvar map = this._map,\n\t\t    spiderfyOnMaxZoom = this.options.spiderfyOnMaxZoom,\n\t\t    showCoverageOnHover = this.options.showCoverageOnHover,\n\t\t    zoomToBoundsOnClick = this.options.zoomToBoundsOnClick,\n\t\t    spiderfyOnEveryZoom = this.options.spiderfyOnEveryZoom;\n\n\t\t//Zoom on cluster click or spiderfy if we are at the lowest level\n\t\tif (spiderfyOnMaxZoom || zoomToBoundsOnClick || spiderfyOnEveryZoom) {\n\t\t\tthis.on('clusterclick clusterkeypress', this._zoomOrSpiderfy, this);\n\t\t}\n\n\t\t//Show convex hull (boundary) polygon on mouse over\n\t\tif (showCoverageOnHover) {\n\t\t\tthis.on('clustermouseover', this._showCoverage, this);\n\t\t\tthis.on('clustermouseout', this._hideCoverage, this);\n\t\t\tmap.on('zoomend', this._hideCoverage, this);\n\t\t}\n\t},\n\n\t_zoomOrSpiderfy: function (e) {\n\t\tvar cluster = e.layer,\n\t\t    bottomCluster = cluster;\n\n\t\tif (e.type === 'clusterkeypress' && e.originalEvent && e.originalEvent.keyCode !== 13) {\n\t\t\treturn;\n\t\t}\n\n\t\twhile (bottomCluster._childClusters.length === 1) {\n\t\t\tbottomCluster = bottomCluster._childClusters[0];\n\t\t}\n\n\t\tif (bottomCluster._zoom === this._maxZoom &&\n\t\t\tbottomCluster._childCount === cluster._childCount &&\n\t\t\tthis.options.spiderfyOnMaxZoom) {\n\n\t\t\t// All child markers are contained in a single cluster from this._maxZoom to this cluster.\n\t\t\tcluster.spiderfy();\n\t\t} else if (this.options.zoomToBoundsOnClick) {\n\t\t\tcluster.zoomToBounds();\n\t\t}\n\n\t\tif (this.options.spiderfyOnEveryZoom) {\n\t\t\tcluster.spiderfy();\n\t\t}\n\n\t\t// Focus the map again for keyboard users.\n\t\tif (e.originalEvent && e.originalEvent.keyCode === 13) {\n\t\t\tthis._map._container.focus();\n\t\t}\n\t},\n\n\t_showCoverage: function (e) {\n\t\tvar map = this._map;\n\t\tif (this._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\t\tif (this._shownPolygon) {\n\t\t\tmap.removeLayer(this._shownPolygon);\n\t\t}\n\t\tif (e.layer.getChildCount() > 2 && e.layer !== this._spiderfied) {\n\t\t\tthis._shownPolygon = new L.Polygon(e.layer.getConvexHull(), this.options.polygonOptions);\n\t\t\tmap.addLayer(this._shownPolygon);\n\t\t}\n\t},\n\n\t_hideCoverage: function () {\n\t\tif (this._shownPolygon) {\n\t\t\tthis._map.removeLayer(this._shownPolygon);\n\t\t\tthis._shownPolygon = null;\n\t\t}\n\t},\n\n\t_unbindEvents: function () {\n\t\tvar spiderfyOnMaxZoom = this.options.spiderfyOnMaxZoom,\n\t\t\tshowCoverageOnHover = this.options.showCoverageOnHover,\n\t\t\tzoomToBoundsOnClick = this.options.zoomToBoundsOnClick,\n\t\t\tspiderfyOnEveryZoom = this.options.spiderfyOnEveryZoom,\n\t\t\tmap = this._map;\n\n\t\tif (spiderfyOnMaxZoom || zoomToBoundsOnClick || spiderfyOnEveryZoom) {\n\t\t\tthis.off('clusterclick clusterkeypress', this._zoomOrSpiderfy, this);\n\t\t}\n\t\tif (showCoverageOnHover) {\n\t\t\tthis.off('clustermouseover', this._showCoverage, this);\n\t\t\tthis.off('clustermouseout', this._hideCoverage, this);\n\t\t\tmap.off('zoomend', this._hideCoverage, this);\n\t\t}\n\t},\n\n\t_zoomEnd: function () {\n\t\tif (!this._map) { //May have been removed from the map by a zoomEnd handler\n\t\t\treturn;\n\t\t}\n\t\tthis._mergeSplitClusters();\n\n\t\tthis._zoom = Math.round(this._map._zoom);\n\t\tthis._currentShownBounds = this._getExpandedVisibleBounds();\n\t},\n\n\t_moveEnd: function () {\n\t\tif (this._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar newBounds = this._getExpandedVisibleBounds();\n\n\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), this._zoom, newBounds);\n\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, Math.round(this._map._zoom), newBounds);\n\n\t\tthis._currentShownBounds = newBounds;\n\t\treturn;\n\t},\n\n\t_generateInitialClusters: function () {\n\t\tvar maxZoom = Math.ceil(this._map.getMaxZoom()),\n\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t\tradius = this.options.maxClusterRadius,\n\t\t\tradiusFn = radius;\n\n\t\t//If we just set maxClusterRadius to a single number, we need to create\n\t\t//a simple function to return that number. Otherwise, we just have to\n\t\t//use the function we've passed in.\n\t\tif (typeof radius !== \"function\") {\n\t\t\tradiusFn = function () { return radius; };\n\t\t}\n\n\t\tif (this.options.disableClusteringAtZoom !== null) {\n\t\t\tmaxZoom = this.options.disableClusteringAtZoom - 1;\n\t\t}\n\t\tthis._maxZoom = maxZoom;\n\t\tthis._gridClusters = {};\n\t\tthis._gridUnclustered = {};\n\n\t\t//Set up DistanceGrids for each zoom\n\t\tfor (var zoom = maxZoom; zoom >= minZoom; zoom--) {\n\t\t\tthis._gridClusters[zoom] = new L.DistanceGrid(radiusFn(zoom));\n\t\t\tthis._gridUnclustered[zoom] = new L.DistanceGrid(radiusFn(zoom));\n\t\t}\n\n\t\t// Instantiate the appropriate L.MarkerCluster class (animated or not).\n\t\tthis._topClusterLevel = new this._markerCluster(this, minZoom - 1);\n\t},\n\n\t//Zoom: Zoom to start adding at (Pass this._maxZoom to start at the bottom)\n\t_addLayer: function (layer, zoom) {\n\t\tvar gridClusters = this._gridClusters,\n\t\t    gridUnclustered = this._gridUnclustered,\n\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t    markerPoint, z;\n\n\t\tif (this.options.singleMarkerMode) {\n\t\t\tthis._overrideMarkerIcon(layer);\n\t\t}\n\n\t\tlayer.on(this._childMarkerEventHandlers, this);\n\n\t\t//Find the lowest zoom level to slot this one in\n\t\tfor (; zoom >= minZoom; zoom--) {\n\t\t\tmarkerPoint = this._map.project(layer.getLatLng(), zoom); // calculate pixel position\n\n\t\t\t//Try find a cluster close by\n\t\t\tvar closest = gridClusters[zoom].getNearObject(markerPoint);\n\t\t\tif (closest) {\n\t\t\t\tclosest._addChild(layer);\n\t\t\t\tlayer.__parent = closest;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Try find a marker close by to form a new cluster with\n\t\t\tclosest = gridUnclustered[zoom].getNearObject(markerPoint);\n\t\t\tif (closest) {\n\t\t\t\tvar parent = closest.__parent;\n\t\t\t\tif (parent) {\n\t\t\t\t\tthis._removeLayer(closest, false);\n\t\t\t\t}\n\n\t\t\t\t//Create new cluster with these 2 in it\n\n\t\t\t\tvar newCluster = new this._markerCluster(this, zoom, closest, layer);\n\t\t\t\tgridClusters[zoom].addObject(newCluster, this._map.project(newCluster._cLatLng, zoom));\n\t\t\t\tclosest.__parent = newCluster;\n\t\t\t\tlayer.__parent = newCluster;\n\n\t\t\t\t//First create any new intermediate parent clusters that don't exist\n\t\t\t\tvar lastParent = newCluster;\n\t\t\t\tfor (z = zoom - 1; z > parent._zoom; z--) {\n\t\t\t\t\tlastParent = new this._markerCluster(this, z, lastParent);\n\t\t\t\t\tgridClusters[z].addObject(lastParent, this._map.project(closest.getLatLng(), z));\n\t\t\t\t}\n\t\t\t\tparent._addChild(lastParent);\n\n\t\t\t\t//Remove closest from this zoom level and any above that it is in, replace with newCluster\n\t\t\t\tthis._removeFromGridUnclustered(closest, zoom);\n\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t//Didn't manage to cluster in at this zoom, record us as a marker here and continue upwards\n\t\t\tgridUnclustered[zoom].addObject(layer, markerPoint);\n\t\t}\n\n\t\t//Didn't get in anything, add us to the top\n\t\tthis._topClusterLevel._addChild(layer);\n\t\tlayer.__parent = this._topClusterLevel;\n\t\treturn;\n\t},\n\n\t/**\n\t * Refreshes the icon of all \"dirty\" visible clusters.\n\t * Non-visible \"dirty\" clusters will be updated when they are added to the map.\n\t * @private\n\t */\n\t_refreshClustersIcons: function () {\n\t\tthis._featureGroup.eachLayer(function (c) {\n\t\t\tif (c instanceof L.MarkerCluster && c._iconNeedsUpdate) {\n\t\t\t\tc._updateIcon();\n\t\t\t}\n\t\t});\n\t},\n\n\t//Enqueue code to fire after the marker expand/contract has happened\n\t_enqueue: function (fn) {\n\t\tthis._queue.push(fn);\n\t\tif (!this._queueTimeout) {\n\t\t\tthis._queueTimeout = setTimeout(L.bind(this._processQueue, this), 300);\n\t\t}\n\t},\n\t_processQueue: function () {\n\t\tfor (var i = 0; i < this._queue.length; i++) {\n\t\t\tthis._queue[i].call(this);\n\t\t}\n\t\tthis._queue.length = 0;\n\t\tclearTimeout(this._queueTimeout);\n\t\tthis._queueTimeout = null;\n\t},\n\n\t//Merge and split any existing clusters that are too big or small\n\t_mergeSplitClusters: function () {\n\t\tvar mapZoom = Math.round(this._map._zoom);\n\n\t\t//In case we are starting to split before the animation finished\n\t\tthis._processQueue();\n\n\t\tif (this._zoom < mapZoom && this._currentShownBounds.intersects(this._getExpandedVisibleBounds())) { //Zoom in, split\n\t\t\tthis._animationStart();\n\t\t\t//Remove clusters now off screen\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), this._zoom, this._getExpandedVisibleBounds());\n\n\t\t\tthis._animationZoomIn(this._zoom, mapZoom);\n\n\t\t} else if (this._zoom > mapZoom) { //Zoom out, merge\n\t\t\tthis._animationStart();\n\n\t\t\tthis._animationZoomOut(this._zoom, mapZoom);\n\t\t} else {\n\t\t\tthis._moveEnd();\n\t\t}\n\t},\n\n\t//Gets the maps visible bounds expanded in each direction by the size of the screen (so the user cannot see an area we do not cover in one pan)\n\t_getExpandedVisibleBounds: function () {\n\t\tif (!this.options.removeOutsideVisibleBounds) {\n\t\t\treturn this._mapBoundsInfinite;\n\t\t} else if (L.Browser.mobile) {\n\t\t\treturn this._checkBoundsMaxLat(this._map.getBounds());\n\t\t}\n\n\t\treturn this._checkBoundsMaxLat(this._map.getBounds().pad(1)); // Padding expands the bounds by its own dimensions but scaled with the given factor.\n\t},\n\n\t/**\n\t * Expands the latitude to Infinity (or -Infinity) if the input bounds reach the map projection maximum defined latitude\n\t * (in the case of Web/Spherical Mercator, it is 85.0511287798 / see https://en.wikipedia.org/wiki/Web_Mercator#Formulas).\n\t * Otherwise, the removeOutsideVisibleBounds option will remove markers beyond that limit, whereas the same markers without\n\t * this option (or outside MCG) will have their position floored (ceiled) by the projection and rendered at that limit,\n\t * making the user think that MCG \"eats\" them and never displays them again.\n\t * @param bounds L.LatLngBounds\n\t * @returns {L.LatLngBounds}\n\t * @private\n\t */\n\t_checkBoundsMaxLat: function (bounds) {\n\t\tvar maxLat = this._maxLat;\n\n\t\tif (maxLat !== undefined) {\n\t\t\tif (bounds.getNorth() >= maxLat) {\n\t\t\t\tbounds._northEast.lat = Infinity;\n\t\t\t}\n\t\t\tif (bounds.getSouth() <= -maxLat) {\n\t\t\t\tbounds._southWest.lat = -Infinity;\n\t\t\t}\n\t\t}\n\n\t\treturn bounds;\n\t},\n\n\t//Shared animation code\n\t_animationAddLayerNonAnimated: function (layer, newCluster) {\n\t\tif (newCluster === layer) {\n\t\t\tthis._featureGroup.addLayer(layer);\n\t\t} else if (newCluster._childCount === 2) {\n\t\t\tnewCluster._addToMap();\n\n\t\t\tvar markers = newCluster.getAllChildMarkers();\n\t\t\tthis._featureGroup.removeLayer(markers[0]);\n\t\t\tthis._featureGroup.removeLayer(markers[1]);\n\t\t} else {\n\t\t\tnewCluster._updateIcon();\n\t\t}\n\t},\n\n\t/**\n\t * Extracts individual (i.e. non-group) layers from a Layer Group.\n\t * @param group to extract layers from.\n\t * @param output {Array} in which to store the extracted layers.\n\t * @returns {*|Array}\n\t * @private\n\t */\n\t_extractNonGroupLayers: function (group, output) {\n\t\tvar layers = group.getLayers(),\n\t\t    i = 0,\n\t\t    layer;\n\n\t\toutput = output || [];\n\n\t\tfor (; i < layers.length; i++) {\n\t\t\tlayer = layers[i];\n\n\t\t\tif (layer instanceof L.LayerGroup) {\n\t\t\t\tthis._extractNonGroupLayers(layer, output);\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\toutput.push(layer);\n\t\t}\n\n\t\treturn output;\n\t},\n\n\t/**\n\t * Implements the singleMarkerMode option.\n\t * @param layer Marker to re-style using the Clusters iconCreateFunction.\n\t * @returns {L.Icon} The newly created icon.\n\t * @private\n\t */\n\t_overrideMarkerIcon: function (layer) {\n\t\tvar icon = layer.options.icon = this.options.iconCreateFunction({\n\t\t\tgetChildCount: function () {\n\t\t\t\treturn 1;\n\t\t\t},\n\t\t\tgetAllChildMarkers: function () {\n\t\t\t\treturn [layer];\n\t\t\t}\n\t\t});\n\n\t\treturn icon;\n\t}\n});\n\n// Constant bounds used in case option \"removeOutsideVisibleBounds\" is set to false.\nL.MarkerClusterGroup.include({\n\t_mapBoundsInfinite: new L.LatLngBounds(new L.LatLng(-Infinity, -Infinity), new L.LatLng(Infinity, Infinity))\n});\n\nL.MarkerClusterGroup.include({\n\t_noAnimation: {\n\t\t//Non Animated versions of everything\n\t\t_animationStart: function () {\n\t\t\t//Do nothing...\n\t\t},\n\t\t_animationZoomIn: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel);\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\n\t\t\t//We didn't actually animate, but we use this event to mean \"clustering animations have finished\"\n\t\t\tthis.fire('animationend');\n\t\t},\n\t\t_animationZoomOut: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel);\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\n\t\t\t//We didn't actually animate, but we use this event to mean \"clustering animations have finished\"\n\t\t\tthis.fire('animationend');\n\t\t},\n\t\t_animationAddLayer: function (layer, newCluster) {\n\t\t\tthis._animationAddLayerNonAnimated(layer, newCluster);\n\t\t}\n\t},\n\n\t_withAnimation: {\n\t\t//Animated versions here\n\t\t_animationStart: function () {\n\t\t\tthis._map._mapPane.className += ' leaflet-cluster-anim';\n\t\t\tthis._inZoomAnimation++;\n\t\t},\n\n\t\t_animationZoomIn: function (previousZoomLevel, newZoomLevel) {\n\t\t\tvar bounds = this._getExpandedVisibleBounds(),\n\t\t\t    fg = this._featureGroup,\n\t\t\t\tminZoom = Math.floor(this._map.getMinZoom()),\n\t\t\t    i;\n\n\t\t\tthis._ignoreMove = true;\n\n\t\t\t//Add all children of current clusters to map and remove those clusters from map\n\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, minZoom, function (c) {\n\t\t\t\tvar startPos = c._latlng,\n\t\t\t\t    markers  = c._markers,\n\t\t\t\t    m;\n\n\t\t\t\tif (!bounds.contains(startPos)) {\n\t\t\t\t\tstartPos = null;\n\t\t\t\t}\n\n\t\t\t\tif (c._isSingleParent() && previousZoomLevel + 1 === newZoomLevel) { //Immediately add the new child and remove us\n\t\t\t\t\tfg.removeLayer(c);\n\t\t\t\t\tc._recursivelyAddChildrenToMap(null, newZoomLevel, bounds);\n\t\t\t\t} else {\n\t\t\t\t\t//Fade out old cluster\n\t\t\t\t\tc.clusterHide();\n\t\t\t\t\tc._recursivelyAddChildrenToMap(startPos, newZoomLevel, bounds);\n\t\t\t\t}\n\n\t\t\t\t//Remove all markers that aren't visible any more\n\t\t\t\t//TODO: Do we actually need to do this on the higher levels too?\n\t\t\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = markers[i];\n\t\t\t\t\tif (!bounds.contains(m._latlng)) {\n\t\t\t\t\t\tfg.removeLayer(m);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t});\n\n\t\t\tthis._forceLayout();\n\n\t\t\t//Update opacities\n\t\t\tthis._topClusterLevel._recursivelyBecomeVisible(bounds, newZoomLevel);\n\t\t\t//TODO Maybe? Update markers in _recursivelyBecomeVisible\n\t\t\tfg.eachLayer(function (n) {\n\t\t\t\tif (!(n instanceof L.MarkerCluster) && n._icon) {\n\t\t\t\t\tn.clusterShow();\n\t\t\t\t}\n\t\t\t});\n\n\t\t\t//update the positions of the just added clusters/markers\n\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, newZoomLevel, function (c) {\n\t\t\t\tc._recursivelyRestoreChildPositions(newZoomLevel);\n\t\t\t});\n\n\t\t\tthis._ignoreMove = false;\n\n\t\t\t//Remove the old clusters and close the zoom animation\n\t\t\tthis._enqueue(function () {\n\t\t\t\t//update the positions of the just added clusters/markers\n\t\t\t\tthis._topClusterLevel._recursively(bounds, previousZoomLevel, minZoom, function (c) {\n\t\t\t\t\tfg.removeLayer(c);\n\t\t\t\t\tc.clusterShow();\n\t\t\t\t});\n\n\t\t\t\tthis._animationEnd();\n\t\t\t});\n\t\t},\n\n\t\t_animationZoomOut: function (previousZoomLevel, newZoomLevel) {\n\t\t\tthis._animationZoomOutSingle(this._topClusterLevel, previousZoomLevel - 1, newZoomLevel);\n\n\t\t\t//Need to add markers for those that weren't on the map before but are now\n\t\t\tthis._topClusterLevel._recursivelyAddChildrenToMap(null, newZoomLevel, this._getExpandedVisibleBounds());\n\t\t\t//Remove markers that were on the map before but won't be now\n\t\t\tthis._topClusterLevel._recursivelyRemoveChildrenFromMap(this._currentShownBounds, Math.floor(this._map.getMinZoom()), previousZoomLevel, this._getExpandedVisibleBounds());\n\t\t},\n\n\t\t_animationAddLayer: function (layer, newCluster) {\n\t\t\tvar me = this,\n\t\t\t    fg = this._featureGroup;\n\n\t\t\tfg.addLayer(layer);\n\t\t\tif (newCluster !== layer) {\n\t\t\t\tif (newCluster._childCount > 2) { //Was already a cluster\n\n\t\t\t\t\tnewCluster._updateIcon();\n\t\t\t\t\tthis._forceLayout();\n\t\t\t\t\tthis._animationStart();\n\n\t\t\t\t\tlayer._setPos(this._map.latLngToLayerPoint(newCluster.getLatLng()));\n\t\t\t\t\tlayer.clusterHide();\n\n\t\t\t\t\tthis._enqueue(function () {\n\t\t\t\t\t\tfg.removeLayer(layer);\n\t\t\t\t\t\tlayer.clusterShow();\n\n\t\t\t\t\t\tme._animationEnd();\n\t\t\t\t\t});\n\n\t\t\t\t} else { //Just became a cluster\n\t\t\t\t\tthis._forceLayout();\n\n\t\t\t\t\tme._animationStart();\n\t\t\t\t\tme._animationZoomOutSingle(newCluster, this._map.getMaxZoom(), this._zoom);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t// Private methods for animated versions.\n\t_animationZoomOutSingle: function (cluster, previousZoomLevel, newZoomLevel) {\n\t\tvar bounds = this._getExpandedVisibleBounds(),\n\t\t\tminZoom = Math.floor(this._map.getMinZoom());\n\n\t\t//Animate all of the markers in the clusters to move to their cluster center point\n\t\tcluster._recursivelyAnimateChildrenInAndAddSelfToMap(bounds, minZoom, previousZoomLevel + 1, newZoomLevel);\n\n\t\tvar me = this;\n\n\t\t//Update the opacity (If we immediately set it they won't animate)\n\t\tthis._forceLayout();\n\t\tcluster._recursivelyBecomeVisible(bounds, newZoomLevel);\n\n\t\t//TODO: Maybe use the transition timing stuff to make this more reliable\n\t\t//When the animations are done, tidy up\n\t\tthis._enqueue(function () {\n\n\t\t\t//This cluster stopped being a cluster before the timeout fired\n\t\t\tif (cluster._childCount === 1) {\n\t\t\t\tvar m = cluster._markers[0];\n\t\t\t\t//If we were in a cluster animation at the time then the opacity and position of our child could be wrong now, so fix it\n\t\t\t\tthis._ignoreMove = true;\n\t\t\t\tm.setLatLng(m.getLatLng());\n\t\t\t\tthis._ignoreMove = false;\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcluster._recursively(bounds, newZoomLevel, minZoom, function (c) {\n\t\t\t\t\tc._recursivelyRemoveChildrenFromMap(bounds, minZoom, previousZoomLevel + 1);\n\t\t\t\t});\n\t\t\t}\n\t\t\tme._animationEnd();\n\t\t});\n\t},\n\n\t_animationEnd: function () {\n\t\tif (this._map) {\n\t\t\tthis._map._mapPane.className = this._map._mapPane.className.replace(' leaflet-cluster-anim', '');\n\t\t}\n\t\tthis._inZoomAnimation--;\n\t\tthis.fire('animationend');\n\t},\n\n\t//Force a browser layout of stuff in the map\n\t// Should apply the current opacity and location to all elements so we can update them again for an animation\n\t_forceLayout: function () {\n\t\t//In my testing this works, infact offsetWidth of any element seems to work.\n\t\t//Could loop all this._layers and do this for each _icon if it stops working\n\n\t\tL.Util.falseFn(document.body.offsetWidth);\n\t}\n});\n\nL.markerClusterGroup = function (options) {\n\treturn new L.MarkerClusterGroup(options);\n};\n", "export var MarkerCluster = L.MarkerCluster = L.Marker.extend({\n\toptions: L.Icon.prototype.options,\n\n\tinitialize: function (group, zoom, a, b) {\n\n\t\tL.Marker.prototype.initialize.call(this, a ? (a._cLatLng || a.getLatLng()) : new L.LatLng(0, 0),\n            { icon: this, pane: group.options.clusterPane });\n\n\t\tthis._group = group;\n\t\tthis._zoom = zoom;\n\n\t\tthis._markers = [];\n\t\tthis._childClusters = [];\n\t\tthis._childCount = 0;\n\t\tthis._iconNeedsUpdate = true;\n\t\tthis._boundsNeedUpdate = true;\n\n\t\tthis._bounds = new L.LatLngBounds();\n\n\t\tif (a) {\n\t\t\tthis._addChild(a);\n\t\t}\n\t\tif (b) {\n\t\t\tthis._addChild(b);\n\t\t}\n\t},\n\n\t//Recursively retrieve all child markers of this cluster\n\tgetAllChildMarkers: function (storageArray, ignoreDraggedMarker) {\n\t\tstorageArray = storageArray || [];\n\n\t\tfor (var i = this._childClusters.length - 1; i >= 0; i--) {\n\t\t\tthis._childClusters[i].getAllChildMarkers(storageArray, ignoreDraggedMarker);\n\t\t}\n\n\t\tfor (var j = this._markers.length - 1; j >= 0; j--) {\n\t\t\tif (ignoreDraggedMarker && this._markers[j].__dragStart) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tstorageArray.push(this._markers[j]);\n\t\t}\n\n\t\treturn storageArray;\n\t},\n\n\t//Returns the count of how many child markers we have\n\tgetChildCount: function () {\n\t\treturn this._childCount;\n\t},\n\n\t//Zoom to the minimum of showing all of the child markers, or the extents of this cluster\n\tzoomToBounds: function (fitBoundsOptions) {\n\t\tvar childClusters = this._childClusters.slice(),\n\t\t\tmap = this._group._map,\n\t\t\tboundsZoom = map.getBoundsZoom(this._bounds),\n\t\t\tzoom = this._zoom + 1,\n\t\t\tmapZoom = map.getZoom(),\n\t\t\ti;\n\n\t\t//calculate how far we need to zoom down to see all of the markers\n\t\twhile (childClusters.length > 0 && boundsZoom > zoom) {\n\t\t\tzoom++;\n\t\t\tvar newClusters = [];\n\t\t\tfor (i = 0; i < childClusters.length; i++) {\n\t\t\t\tnewClusters = newClusters.concat(childClusters[i]._childClusters);\n\t\t\t}\n\t\t\tchildClusters = newClusters;\n\t\t}\n\n\t\tif (boundsZoom > zoom) {\n\t\t\tthis._group._map.setView(this._latlng, zoom);\n\t\t} else if (boundsZoom <= mapZoom) { //If fitBounds wouldn't zoom us down, zoom us down instead\n\t\t\tthis._group._map.setView(this._latlng, mapZoom + 1);\n\t\t} else {\n\t\t\tthis._group._map.fitBounds(this._bounds, fitBoundsOptions);\n\t\t}\n\t},\n\n\tgetBounds: function () {\n\t\tvar bounds = new L.LatLngBounds();\n\t\tbounds.extend(this._bounds);\n\t\treturn bounds;\n\t},\n\n\t_updateIcon: function () {\n\t\tthis._iconNeedsUpdate = true;\n\t\tif (this._icon) {\n\t\t\tthis.setIcon(this);\n\t\t}\n\t},\n\n\t//Cludge for Icon, we pretend to be an icon for performance\n\tcreateIcon: function () {\n\t\tif (this._iconNeedsUpdate) {\n\t\t\tthis._iconObj = this._group.options.iconCreateFunction(this);\n\t\t\tthis._iconNeedsUpdate = false;\n\t\t}\n\t\treturn this._iconObj.createIcon();\n\t},\n\tcreateShadow: function () {\n\t\treturn this._iconObj.createShadow();\n\t},\n\n\n\t_addChild: function (new1, isNotificationFromChild) {\n\n\t\tthis._iconNeedsUpdate = true;\n\n\t\tthis._boundsNeedUpdate = true;\n\t\tthis._setClusterCenter(new1);\n\n\t\tif (new1 instanceof L.MarkerCluster) {\n\t\t\tif (!isNotificationFromChild) {\n\t\t\t\tthis._childClusters.push(new1);\n\t\t\t\tnew1.__parent = this;\n\t\t\t}\n\t\t\tthis._childCount += new1._childCount;\n\t\t} else {\n\t\t\tif (!isNotificationFromChild) {\n\t\t\t\tthis._markers.push(new1);\n\t\t\t}\n\t\t\tthis._childCount++;\n\t\t}\n\n\t\tif (this.__parent) {\n\t\t\tthis.__parent._addChild(new1, true);\n\t\t}\n\t},\n\n\t/**\n\t * Makes sure the cluster center is set. If not, uses the child center if it is a cluster, or the marker position.\n\t * @param child L.MarkerCluster|L.Marker that will be used as cluster center if not defined yet.\n\t * @private\n\t */\n\t_setClusterCenter: function (child) {\n\t\tif (!this._cLatLng) {\n\t\t\t// when clustering, take position of the first point as the cluster center\n\t\t\tthis._cLatLng = child._cLatLng || child._latlng;\n\t\t}\n\t},\n\n\t/**\n\t * Assigns impossible bounding values so that the next extend entirely determines the new bounds.\n\t * This method avoids having to trash the previous L.LatLngBounds object and to create a new one, which is much slower for this class.\n\t * As long as the bounds are not extended, most other methods would probably fail, as they would with bounds initialized but not extended.\n\t * @private\n\t */\n\t_resetBounds: function () {\n\t\tvar bounds = this._bounds;\n\n\t\tif (bounds._southWest) {\n\t\t\tbounds._southWest.lat = Infinity;\n\t\t\tbounds._southWest.lng = Infinity;\n\t\t}\n\t\tif (bounds._northEast) {\n\t\t\tbounds._northEast.lat = -Infinity;\n\t\t\tbounds._northEast.lng = -Infinity;\n\t\t}\n\t},\n\n\t_recalculateBounds: function () {\n\t\tvar markers = this._markers,\n\t\t    childClusters = this._childClusters,\n\t\t    latSum = 0,\n\t\t    lngSum = 0,\n\t\t    totalCount = this._childCount,\n\t\t    i, child, childLatLng, childCount;\n\n\t\t// Case where all markers are removed from the map and we are left with just an empty _topClusterLevel.\n\t\tif (totalCount === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\t// Reset rather than creating a new object, for performance.\n\t\tthis._resetBounds();\n\n\t\t// Child markers.\n\t\tfor (i = 0; i < markers.length; i++) {\n\t\t\tchildLatLng = markers[i]._latlng;\n\n\t\t\tthis._bounds.extend(childLatLng);\n\n\t\t\tlatSum += childLatLng.lat;\n\t\t\tlngSum += childLatLng.lng;\n\t\t}\n\n\t\t// Child clusters.\n\t\tfor (i = 0; i < childClusters.length; i++) {\n\t\t\tchild = childClusters[i];\n\n\t\t\t// Re-compute child bounds and weighted position first if necessary.\n\t\t\tif (child._boundsNeedUpdate) {\n\t\t\t\tchild._recalculateBounds();\n\t\t\t}\n\n\t\t\tthis._bounds.extend(child._bounds);\n\n\t\t\tchildLatLng = child._wLatLng;\n\t\t\tchildCount = child._childCount;\n\n\t\t\tlatSum += childLatLng.lat * childCount;\n\t\t\tlngSum += childLatLng.lng * childCount;\n\t\t}\n\n\t\tthis._latlng = this._wLatLng = new L.LatLng(latSum / totalCount, lngSum / totalCount);\n\n\t\t// Reset dirty flag.\n\t\tthis._boundsNeedUpdate = false;\n\t},\n\n\t//Set our markers position as given and add it to the map\n\t_addToMap: function (startPos) {\n\t\tif (startPos) {\n\t\t\tthis._backupLatlng = this._latlng;\n\t\t\tthis.setLatLng(startPos);\n\t\t}\n\t\tthis._group._featureGroup.addLayer(this);\n\t},\n\n\t_recursivelyAnimateChildrenIn: function (bounds, center, maxZoom) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom(), maxZoom - 1,\n\t\t\tfunction (c) {\n\t\t\t\tvar markers = c._markers,\n\t\t\t\t\ti, m;\n\t\t\t\tfor (i = markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = markers[i];\n\n\t\t\t\t\t//Only do it if the icon is still on the map\n\t\t\t\t\tif (m._icon) {\n\t\t\t\t\t\tm._setPos(center);\n\t\t\t\t\t\tm.clusterHide();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\tvar childClusters = c._childClusters,\n\t\t\t\t\tj, cm;\n\t\t\t\tfor (j = childClusters.length - 1; j >= 0; j--) {\n\t\t\t\t\tcm = childClusters[j];\n\t\t\t\t\tif (cm._icon) {\n\t\t\t\t\t\tcm._setPos(center);\n\t\t\t\t\t\tcm.clusterHide();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyAnimateChildrenInAndAddSelfToMap: function (bounds, mapMinZoom, previousZoomLevel, newZoomLevel) {\n\t\tthis._recursively(bounds, newZoomLevel, mapMinZoom,\n\t\t\tfunction (c) {\n\t\t\t\tc._recursivelyAnimateChildrenIn(bounds, c._group._map.latLngToLayerPoint(c.getLatLng()).round(), previousZoomLevel);\n\n\t\t\t\t//TODO: depthToAnimateIn affects _isSingleParent, if there is a multizoom we may/may not be.\n\t\t\t\t//As a hack we only do a animation free zoom on a single level zoom, if someone does multiple levels then we always animate\n\t\t\t\tif (c._isSingleParent() && previousZoomLevel - 1 === newZoomLevel) {\n\t\t\t\t\tc.clusterShow();\n\t\t\t\t\tc._recursivelyRemoveChildrenFromMap(bounds, mapMinZoom, previousZoomLevel); //Immediately remove our children as we are replacing them. TODO previousBounds not bounds\n\t\t\t\t} else {\n\t\t\t\t\tc.clusterHide();\n\t\t\t\t}\n\n\t\t\t\tc._addToMap();\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyBecomeVisible: function (bounds, zoomLevel) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom(), zoomLevel, null, function (c) {\n\t\t\tc.clusterShow();\n\t\t});\n\t},\n\n\t_recursivelyAddChildrenToMap: function (startPos, zoomLevel, bounds) {\n\t\tthis._recursively(bounds, this._group._map.getMinZoom() - 1, zoomLevel,\n\t\t\tfunction (c) {\n\t\t\t\tif (zoomLevel === c._zoom) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t//Add our child markers at startPos (so they can be animated out)\n\t\t\t\tfor (var i = c._markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tvar nm = c._markers[i];\n\n\t\t\t\t\tif (!bounds.contains(nm._latlng)) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (startPos) {\n\t\t\t\t\t\tnm._backupLatlng = nm.getLatLng();\n\n\t\t\t\t\t\tnm.setLatLng(startPos);\n\t\t\t\t\t\tif (nm.clusterHide) {\n\t\t\t\t\t\t\tnm.clusterHide();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tc._group._featureGroup.addLayer(nm);\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\tc._addToMap(startPos);\n\t\t\t}\n\t\t);\n\t},\n\n\t_recursivelyRestoreChildPositions: function (zoomLevel) {\n\t\t//Fix positions of child markers\n\t\tfor (var i = this._markers.length - 1; i >= 0; i--) {\n\t\t\tvar nm = this._markers[i];\n\t\t\tif (nm._backupLatlng) {\n\t\t\t\tnm.setLatLng(nm._backupLatlng);\n\t\t\t\tdelete nm._backupLatlng;\n\t\t\t}\n\t\t}\n\n\t\tif (zoomLevel - 1 === this._zoom) {\n\t\t\t//Reposition child clusters\n\t\t\tfor (var j = this._childClusters.length - 1; j >= 0; j--) {\n\t\t\t\tthis._childClusters[j]._restorePosition();\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var k = this._childClusters.length - 1; k >= 0; k--) {\n\t\t\t\tthis._childClusters[k]._recursivelyRestoreChildPositions(zoomLevel);\n\t\t\t}\n\t\t}\n\t},\n\n\t_restorePosition: function () {\n\t\tif (this._backupLatlng) {\n\t\t\tthis.setLatLng(this._backupLatlng);\n\t\t\tdelete this._backupLatlng;\n\t\t}\n\t},\n\n\t//exceptBounds: If set, don't remove any markers/clusters in it\n\t_recursivelyRemoveChildrenFromMap: function (previousBounds, mapMinZoom, zoomLevel, exceptBounds) {\n\t\tvar m, i;\n\t\tthis._recursively(previousBounds, mapMinZoom - 1, zoomLevel - 1,\n\t\t\tfunction (c) {\n\t\t\t\t//Remove markers at every level\n\t\t\t\tfor (i = c._markers.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = c._markers[i];\n\t\t\t\t\tif (!exceptBounds || !exceptBounds.contains(m._latlng)) {\n\t\t\t\t\t\tc._group._featureGroup.removeLayer(m);\n\t\t\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\t\t\tm.clusterShow();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction (c) {\n\t\t\t\t//Remove child clusters at just the bottom level\n\t\t\t\tfor (i = c._childClusters.length - 1; i >= 0; i--) {\n\t\t\t\t\tm = c._childClusters[i];\n\t\t\t\t\tif (!exceptBounds || !exceptBounds.contains(m._latlng)) {\n\t\t\t\t\t\tc._group._featureGroup.removeLayer(m);\n\t\t\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\t\t\tm.clusterShow();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t},\n\n\t//Run the given functions recursively to this and child clusters\n\t// boundsToApplyTo: a L.LatLngBounds representing the bounds of what clusters to recurse in to\n\t// zoomLevelToStart: zoom level to start running functions (inclusive)\n\t// zoomLevelToStop: zoom level to stop running functions (inclusive)\n\t// runAtEveryLevel: function that takes an L.MarkerCluster as an argument that should be applied on every level\n\t// runAtBottomLevel: function that takes an L.MarkerCluster as an argument that should be applied at only the bottom level\n\t_recursively: function (boundsToApplyTo, zoomLevelToStart, zoomLevelToStop, runAtEveryLevel, runAtBottomLevel) {\n\t\tvar childClusters = this._childClusters,\n\t\t    zoom = this._zoom,\n\t\t    i, c;\n\n\t\tif (zoomLevelToStart <= zoom) {\n\t\t\tif (runAtEveryLevel) {\n\t\t\t\trunAtEveryLevel(this);\n\t\t\t}\n\t\t\tif (runAtBottomLevel && zoom === zoomLevelToStop) {\n\t\t\t\trunAtBottomLevel(this);\n\t\t\t}\n\t\t}\n\n\t\tif (zoom < zoomLevelToStart || zoom < zoomLevelToStop) {\n\t\t\tfor (i = childClusters.length - 1; i >= 0; i--) {\n\t\t\t\tc = childClusters[i];\n\t\t\t\tif (c._boundsNeedUpdate) {\n\t\t\t\t\tc._recalculateBounds();\n\t\t\t\t}\n\t\t\t\tif (boundsToApplyTo.intersects(c._bounds)) {\n\t\t\t\t\tc._recursively(boundsToApplyTo, zoomLevelToStart, zoomLevelToStop, runAtEveryLevel, runAtBottomLevel);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\t//Returns true if we are the parent of only one cluster and that cluster is the same as us\n\t_isSingleParent: function () {\n\t\t//Don't need to check this._markers as the rest won't work if there are any\n\t\treturn this._childClusters.length > 0 && this._childClusters[0]._childCount === this._childCount;\n\t}\n});\n\n", "/*\n* Extends <PERSON><PERSON> to include two extra methods: clusterHide and clusterShow.\n* \n* They work as setOpacity(0) and setOpacity(1) respectively, but\n* don't overwrite the options.opacity\n* \n*/\n\nL.Marker.include({\n\tclusterHide: function () {\n\t\tvar backup = this.options.opacity;\n\t\tthis.setOpacity(0);\n\t\tthis.options.opacity = backup;\n\t\treturn this;\n\t},\n\t\n\tclusterShow: function () {\n\t\treturn this.setOpacity(this.options.opacity);\n\t}\n});\n\n\n", "\nL.DistanceGrid = function (cellSize) {\n\tthis._cellSize = cellSize;\n\tthis._sqCellSize = cellSize * cellSize;\n\tthis._grid = {};\n\tthis._objectPoint = { };\n};\n\nL.DistanceGrid.prototype = {\n\n\taddObject: function (obj, point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    grid = this._grid,\n\t\t    row = grid[y] = grid[y] || {},\n\t\t    cell = row[x] = row[x] || [],\n\t\t    stamp = L.Util.stamp(obj);\n\n\t\tthis._objectPoint[stamp] = point;\n\n\t\tcell.push(obj);\n\t},\n\n\tupdateObject: function (obj, point) {\n\t\tthis.removeObject(obj);\n\t\tthis.addObject(obj, point);\n\t},\n\n\t//Returns true if the object was found\n\tremoveObject: function (obj, point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    grid = this._grid,\n\t\t    row = grid[y] = grid[y] || {},\n\t\t    cell = row[x] = row[x] || [],\n\t\t    i, len;\n\n\t\tdelete this._objectPoint[L.Util.stamp(obj)];\n\n\t\tfor (i = 0, len = cell.length; i < len; i++) {\n\t\t\tif (cell[i] === obj) {\n\n\t\t\t\tcell.splice(i, 1);\n\n\t\t\t\tif (len === 1) {\n\t\t\t\t\tdelete row[x];\n\t\t\t\t}\n\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t},\n\n\teachObject: function (fn, context) {\n\t\tvar i, j, k, len, row, cell, removed,\n\t\t    grid = this._grid;\n\n\t\tfor (i in grid) {\n\t\t\trow = grid[i];\n\n\t\t\tfor (j in row) {\n\t\t\t\tcell = row[j];\n\n\t\t\t\tfor (k = 0, len = cell.length; k < len; k++) {\n\t\t\t\t\tremoved = fn.call(context, cell[k]);\n\t\t\t\t\tif (removed) {\n\t\t\t\t\t\tk--;\n\t\t\t\t\t\tlen--;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\n\tgetNearObject: function (point) {\n\t\tvar x = this._getCoord(point.x),\n\t\t    y = this._getCoord(point.y),\n\t\t    i, j, k, row, cell, len, obj, dist,\n\t\t    objectPoint = this._objectPoint,\n\t\t    closestDistSq = this._sqCellSize,\n\t\t    closest = null;\n\n\t\tfor (i = y - 1; i <= y + 1; i++) {\n\t\t\trow = this._grid[i];\n\t\t\tif (row) {\n\n\t\t\t\tfor (j = x - 1; j <= x + 1; j++) {\n\t\t\t\t\tcell = row[j];\n\t\t\t\t\tif (cell) {\n\n\t\t\t\t\t\tfor (k = 0, len = cell.length; k < len; k++) {\n\t\t\t\t\t\t\tobj = cell[k];\n\t\t\t\t\t\t\tdist = this._sqDist(objectPoint[L.Util.stamp(obj)], point);\n\t\t\t\t\t\t\tif (dist < closestDistSq ||\n\t\t\t\t\t\t\t\tdist <= closestDistSq && closest === null) {\n\t\t\t\t\t\t\t\tclosestDistSq = dist;\n\t\t\t\t\t\t\t\tclosest = obj;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn closest;\n\t},\n\n\t_getCoord: function (x) {\n\t\tvar coord = Math.floor(x / this._cellSize);\n\t\treturn isFinite(coord) ? coord : x;\n\t},\n\n\t_sqDist: function (p, p2) {\n\t\tvar dx = p2.x - p.x,\n\t\t    dy = p2.y - p.y;\n\t\treturn dx * dx + dy * dy;\n\t}\n};\n", "/* Copyright (c) 2012 the authors listed at the following URL, and/or\nthe authors of referenced articles or incorporated external code:\nhttp://en.literateprograms.org/Quickhull_(Javascript)?action=history&offset=20120410175256\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nRetrieved from: http://en.literateprograms.org/Quickhull_(Javascript)?oldid=18434\n*/\n\n(function () {\n\tL.QuickHull = {\n\n\t\t/*\n\t\t * @param {Object} cpt a point to be measured from the baseline\n\t\t * @param {Array} bl the baseline, as represented by a two-element\n\t\t *   array of latlng objects.\n\t\t * @returns {Number} an approximate distance measure\n\t\t */\n\t\tgetDistant: function (cpt, bl) {\n\t\t\tvar vY = bl[1].lat - bl[0].lat,\n\t\t\t\tvX = bl[0].lng - bl[1].lng;\n\t\t\treturn (vX * (cpt.lat - bl[0].lat) + vY * (cpt.lng - bl[0].lng));\n\t\t},\n\n\t\t/*\n\t\t * @param {Array} baseLine a two-element array of latlng objects\n\t\t *   representing the baseline to project from\n\t\t * @param {Array} latLngs an array of latlng objects\n\t\t * @returns {Object} the maximum point and all new points to stay\n\t\t *   in consideration for the hull.\n\t\t */\n\t\tfindMostDistantPointFromBaseLine: function (baseLine, latLngs) {\n\t\t\tvar maxD = 0,\n\t\t\t\tmaxPt = null,\n\t\t\t\tnewPoints = [],\n\t\t\t\ti, pt, d;\n\n\t\t\tfor (i = latLngs.length - 1; i >= 0; i--) {\n\t\t\t\tpt = latLngs[i];\n\t\t\t\td = this.getDistant(pt, baseLine);\n\n\t\t\t\tif (d > 0) {\n\t\t\t\t\tnewPoints.push(pt);\n\t\t\t\t} else {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (d > maxD) {\n\t\t\t\t\tmaxD = d;\n\t\t\t\t\tmaxPt = pt;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn { maxPoint: maxPt, newPoints: newPoints };\n\t\t},\n\n\n\t\t/*\n\t\t * Given a baseline, compute the convex hull of latLngs as an array\n\t\t * of latLngs.\n\t\t *\n\t\t * @param {Array} latLngs\n\t\t * @returns {Array}\n\t\t */\n\t\tbuildConvexHull: function (baseLine, latLngs) {\n\t\t\tvar convexHullBaseLines = [],\n\t\t\t\tt = this.findMostDistantPointFromBaseLine(baseLine, latLngs);\n\n\t\t\tif (t.maxPoint) { // if there is still a point \"outside\" the base line\n\t\t\t\tconvexHullBaseLines =\n\t\t\t\t\tconvexHullBaseLines.concat(\n\t\t\t\t\t\tthis.buildConvexHull([baseLine[0], t.maxPoint], t.newPoints)\n\t\t\t\t\t);\n\t\t\t\tconvexHullBaseLines =\n\t\t\t\t\tconvexHullBaseLines.concat(\n\t\t\t\t\t\tthis.buildConvexHull([t.maxPoint, baseLine[1]], t.newPoints)\n\t\t\t\t\t);\n\t\t\t\treturn convexHullBaseLines;\n\t\t\t} else {  // if there is no more point \"outside\" the base line, the current base line is part of the convex hull\n\t\t\t\treturn [baseLine[0]];\n\t\t\t}\n\t\t},\n\n\t\t/*\n\t\t * Given an array of latlngs, compute a convex hull as an array\n\t\t * of latlngs\n\t\t *\n\t\t * @param {Array} latLngs\n\t\t * @returns {Array}\n\t\t */\n\t\tgetConvexHull: function (latLngs) {\n\t\t\t// find first baseline\n\t\t\tvar maxLat = false, minLat = false,\n\t\t\t\tmaxLng = false, minLng = false,\n\t\t\t\tmaxLatPt = null, minLatPt = null,\n\t\t\t\tmaxLngPt = null, minLngPt = null,\n\t\t\t\tmaxPt = null, minPt = null,\n\t\t\t\ti;\n\n\t\t\tfor (i = latLngs.length - 1; i >= 0; i--) {\n\t\t\t\tvar pt = latLngs[i];\n\t\t\t\tif (maxLat === false || pt.lat > maxLat) {\n\t\t\t\t\tmaxLatPt = pt;\n\t\t\t\t\tmaxLat = pt.lat;\n\t\t\t\t}\n\t\t\t\tif (minLat === false || pt.lat < minLat) {\n\t\t\t\t\tminLatPt = pt;\n\t\t\t\t\tminLat = pt.lat;\n\t\t\t\t}\n\t\t\t\tif (maxLng === false || pt.lng > maxLng) {\n\t\t\t\t\tmaxLngPt = pt;\n\t\t\t\t\tmaxLng = pt.lng;\n\t\t\t\t}\n\t\t\t\tif (minLng === false || pt.lng < minLng) {\n\t\t\t\t\tminLngPt = pt;\n\t\t\t\t\tminLng = pt.lng;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tif (minLat !== maxLat) {\n\t\t\t\tminPt = minLatPt;\n\t\t\t\tmaxPt = maxLatPt;\n\t\t\t} else {\n\t\t\t\tminPt = minLngPt;\n\t\t\t\tmaxPt = maxLngPt;\n\t\t\t}\n\n\t\t\tvar ch = [].concat(this.buildConvexHull([minPt, maxPt], latLngs),\n\t\t\t\t\t\t\t\tthis.buildConvexHull([maxPt, minPt], latLngs));\n\t\t\treturn ch;\n\t\t}\n\t};\n}());\n\nL.MarkerCluster.include({\n\tgetConvexHull: function () {\n\t\tvar childMarkers = this.getAllChildMarkers(),\n\t\t\tpoints = [],\n\t\t\tp, i;\n\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tp = childMarkers[i].getLatLng();\n\t\t\tpoints.push(p);\n\t\t}\n\n\t\treturn L.QuickHull.getConvexHull(points);\n\t}\n});\n", "//This code is 100% based on https://github.com/jawj/OverlappingMarkerSpiderfier-Leaflet\n//Huge thanks to jawj for implementing it first to make my job easy :-)\n\nL.MarkerCluster.include({\n\n\t_2PI: Math.PI * 2,\n\t_circleFootSeparation: 25, //related to circumference of circle\n\t_circleStartAngle: 0,\n\n\t_spiralFootSeparation:  28, //related to size of spiral (experiment!)\n\t_spiralLengthStart: 11,\n\t_spiralLengthFactor: 5,\n\n\t_circleSpiralSwitchover: 9, //show spiral instead of circle from this marker count upwards.\n\t\t\t\t\t\t\t\t// 0 -> always spiral; Infinity -> always circle\n\n\tspiderfy: function () {\n\t\tif (this._group._spiderfied === this || this._group._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\n\t\tvar childMarkers = this.getAllChildMarkers(null, true),\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tcenter = map.latLngToLayerPoint(this._latlng),\n\t\t\tpositions;\n\n\t\tthis._group._unspiderfy();\n\t\tthis._group._spiderfied = this;\n\n\t\t//TODO Maybe: childMarkers order by distance to center\n\n\t\tif (this._group.options.spiderfyShapePositions) {\n\t\t\tpositions = this._group.options.spiderfyShapePositions(childMarkers.length, center);\n\t\t} else if (childMarkers.length >= this._circleSpiralSwitchover) {\n\t\t\tpositions = this._generatePointsSpiral(childMarkers.length, center);\n\t\t} else {\n\t\t\tcenter.y += 10; // Otherwise circles look wrong => hack for standard blue icon, renders differently for other icons.\n\t\t\tpositions = this._generatePointsCircle(childMarkers.length, center);\n\t\t}\n\n\t\tthis._animationSpiderfy(childMarkers, positions);\n\t},\n\n\tunspiderfy: function (zoomDetails) {\n\t\t/// <param Name=\"zoomDetails\">Argument from zoomanim if being called in a zoom animation or null otherwise</param>\n\t\tif (this._group._inZoomAnimation) {\n\t\t\treturn;\n\t\t}\n\t\tthis._animationUnspiderfy(zoomDetails);\n\n\t\tthis._group._spiderfied = null;\n\t},\n\n\t_generatePointsCircle: function (count, centerPt) {\n\t\tvar circumference = this._group.options.spiderfyDistanceMultiplier * this._circleFootSeparation * (2 + count),\n\t\t\tlegLength = circumference / this._2PI,  //radius from circumference\n\t\t\tangleStep = this._2PI / count,\n\t\t\tres = [],\n\t\t\ti, angle;\n\n\t\tlegLength = Math.max(legLength, 35); // Minimum distance to get outside the cluster icon.\n\n\t\tres.length = count;\n\n\t\tfor (i = 0; i < count; i++) { // Clockwise, like spiral.\n\t\t\tangle = this._circleStartAngle + i * angleStep;\n\t\t\tres[i] = new L.Point(centerPt.x + legLength * Math.cos(angle), centerPt.y + legLength * Math.sin(angle))._round();\n\t\t}\n\n\t\treturn res;\n\t},\n\n\t_generatePointsSpiral: function (count, centerPt) {\n\t\tvar spiderfyDistanceMultiplier = this._group.options.spiderfyDistanceMultiplier,\n\t\t\tlegLength = spiderfyDistanceMultiplier * this._spiralLengthStart,\n\t\t\tseparation = spiderfyDistanceMultiplier * this._spiralFootSeparation,\n\t\t\tlengthFactor = spiderfyDistanceMultiplier * this._spiralLengthFactor * this._2PI,\n\t\t\tangle = 0,\n\t\t\tres = [],\n\t\t\ti;\n\n\t\tres.length = count;\n\n\t\t// Higher index, closer position to cluster center.\n\t\tfor (i = count; i >= 0; i--) {\n\t\t\t// Skip the first position, so that we are already farther from center and we avoid\n\t\t\t// being under the default cluster icon (especially important for Circle Markers).\n\t\t\tif (i < count) {\n\t\t\t\tres[i] = new L.Point(centerPt.x + legLength * Math.cos(angle), centerPt.y + legLength * Math.sin(angle))._round();\n\t\t\t}\n\t\t\tangle += separation / legLength + i * 0.0005;\n\t\t\tlegLength += lengthFactor / angle;\n\t\t}\n\t\treturn res;\n\t},\n\n\t_noanimationUnspiderfy: function () {\n\t\tvar group = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tchildMarkers = this.getAllChildMarkers(null, true),\n\t\t\tm, i;\n\n\t\tgroup._ignoreMove = true;\n\n\t\tthis.setOpacity(1);\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tm = childMarkers[i];\n\n\t\t\tfg.removeLayer(m);\n\n\t\t\tif (m._preSpiderfyLatlng) {\n\t\t\t\tm.setLatLng(m._preSpiderfyLatlng);\n\t\t\t\tdelete m._preSpiderfyLatlng;\n\t\t\t}\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(0);\n\t\t\t}\n\n\t\t\tif (m._spiderLeg) {\n\t\t\t\tmap.removeLayer(m._spiderLeg);\n\t\t\t\tdelete m._spiderLeg;\n\t\t\t}\n\t\t}\n\n\t\tgroup.fire('unspiderfied', {\n\t\t\tcluster: this,\n\t\t\tmarkers: childMarkers\n\t\t});\n\t\tgroup._ignoreMove = false;\n\t\tgroup._spiderfied = null;\n\t}\n});\n\n//Non Animated versions of everything\nL.MarkerClusterNonAnimated = L.MarkerCluster.extend({\n\t_animationSpiderfy: function (childMarkers, positions) {\n\t\tvar group = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tlegOptions = this._group.options.spiderLegPolylineOptions,\n\t\t\ti, m, leg, newPos;\n\n\t\tgroup._ignoreMove = true;\n\n\t\t// Traverse in ascending order to make sure that inner circleMarkers are on top of further legs. Normal markers are re-ordered by newPosition.\n\t\t// The reverse order trick no longer improves performance on modern browsers.\n\t\tfor (i = 0; i < childMarkers.length; i++) {\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\t\t\tm = childMarkers[i];\n\n\t\t\t// Add the leg before the marker, so that in case the latter is a circleMarker, the leg is behind it.\n\t\t\tleg = new L.Polyline([this._latlng, newPos], legOptions);\n\t\t\tmap.addLayer(leg);\n\t\t\tm._spiderLeg = leg;\n\n\t\t\t// Now add the marker.\n\t\t\tm._preSpiderfyLatlng = m._latlng;\n\t\t\tm.setLatLng(newPos);\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(1000000); //Make these appear on top of EVERYTHING\n\t\t\t}\n\n\t\t\tfg.addLayer(m);\n\t\t}\n\t\tthis.setOpacity(0.3);\n\n\t\tgroup._ignoreMove = false;\n\t\tgroup.fire('spiderfied', {\n\t\t\tcluster: this,\n\t\t\tmarkers: childMarkers\n\t\t});\n\t},\n\n\t_animationUnspiderfy: function () {\n\t\tthis._noanimationUnspiderfy();\n\t}\n});\n\n//Animated versions here\nL.MarkerCluster.include({\n\n\t_animationSpiderfy: function (childMarkers, positions) {\n\t\tvar me = this,\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tthisLayerLatLng = this._latlng,\n\t\t\tthisLayerPos = map.latLngToLayerPoint(thisLayerLatLng),\n\t\t\tsvg = L.Path.SVG,\n\t\t\tlegOptions = L.extend({}, this._group.options.spiderLegPolylineOptions), // Copy the options so that we can modify them for animation.\n\t\t\tfinalLegOpacity = legOptions.opacity,\n\t\t\ti, m, leg, legPath, legLength, newPos;\n\n\t\tif (finalLegOpacity === undefined) {\n\t\t\tfinalLegOpacity = L.MarkerClusterGroup.prototype.options.spiderLegPolylineOptions.opacity;\n\t\t}\n\n\t\tif (svg) {\n\t\t\t// If the initial opacity of the spider leg is not 0 then it appears before the animation starts.\n\t\t\tlegOptions.opacity = 0;\n\n\t\t\t// Add the class for CSS transitions.\n\t\t\tlegOptions.className = (legOptions.className || '') + ' leaflet-cluster-spider-leg';\n\t\t} else {\n\t\t\t// Make sure we have a defined opacity.\n\t\t\tlegOptions.opacity = finalLegOpacity;\n\t\t}\n\n\t\tgroup._ignoreMove = true;\n\n\t\t// Add markers and spider legs to map, hidden at our center point.\n\t\t// Traverse in ascending order to make sure that inner circleMarkers are on top of further legs. Normal markers are re-ordered by newPosition.\n\t\t// The reverse order trick no longer improves performance on modern browsers.\n\t\tfor (i = 0; i < childMarkers.length; i++) {\n\t\t\tm = childMarkers[i];\n\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\n\t\t\t// Add the leg before the marker, so that in case the latter is a circleMarker, the leg is behind it.\n\t\t\tleg = new L.Polyline([thisLayerLatLng, newPos], legOptions);\n\t\t\tmap.addLayer(leg);\n\t\t\tm._spiderLeg = leg;\n\n\t\t\t// Explanations: https://jakearchibald.com/2013/animated-line-drawing-svg/\n\t\t\t// In our case the transition property is declared in the CSS file.\n\t\t\tif (svg) {\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegLength = legPath.getTotalLength() + 0.1; // Need a small extra length to avoid remaining dot in Firefox.\n\t\t\t\tlegPath.style.strokeDasharray = legLength; // Just 1 length is enough, it will be duplicated.\n\t\t\t\tlegPath.style.strokeDashoffset = legLength;\n\t\t\t}\n\n\t\t\t// If it is a marker, add it now and we'll animate it out\n\t\t\tif (m.setZIndexOffset) {\n\t\t\t\tm.setZIndexOffset(1000000); // Make normal markers appear on top of EVERYTHING\n\t\t\t}\n\t\t\tif (m.clusterHide) {\n\t\t\t\tm.clusterHide();\n\t\t\t}\n\t\t\t\n\t\t\t// Vectors just get immediately added\n\t\t\tfg.addLayer(m);\n\n\t\t\tif (m._setPos) {\n\t\t\t\tm._setPos(thisLayerPos);\n\t\t\t}\n\t\t}\n\n\t\tgroup._forceLayout();\n\t\tgroup._animationStart();\n\n\t\t// Reveal markers and spider legs.\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tnewPos = map.layerPointToLatLng(positions[i]);\n\t\t\tm = childMarkers[i];\n\n\t\t\t//Move marker to new position\n\t\t\tm._preSpiderfyLatlng = m._latlng;\n\t\t\tm.setLatLng(newPos);\n\t\t\t\n\t\t\tif (m.clusterShow) {\n\t\t\t\tm.clusterShow();\n\t\t\t}\n\n\t\t\t// Animate leg (animation is actually delegated to CSS transition).\n\t\t\tif (svg) {\n\t\t\t\tleg = m._spiderLeg;\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegPath.style.strokeDashoffset = 0;\n\t\t\t\t//legPath.style.strokeOpacity = finalLegOpacity;\n\t\t\t\tleg.setStyle({opacity: finalLegOpacity});\n\t\t\t}\n\t\t}\n\t\tthis.setOpacity(0.3);\n\n\t\tgroup._ignoreMove = false;\n\n\t\tsetTimeout(function () {\n\t\t\tgroup._animationEnd();\n\t\t\tgroup.fire('spiderfied', {\n\t\t\t\tcluster: me,\n\t\t\t\tmarkers: childMarkers\n\t\t\t});\n\t\t}, 200);\n\t},\n\n\t_animationUnspiderfy: function (zoomDetails) {\n\t\tvar me = this,\n\t\t\tgroup = this._group,\n\t\t\tmap = group._map,\n\t\t\tfg = group._featureGroup,\n\t\t\tthisLayerPos = zoomDetails ? map._latLngToNewLayerPoint(this._latlng, zoomDetails.zoom, zoomDetails.center) : map.latLngToLayerPoint(this._latlng),\n\t\t\tchildMarkers = this.getAllChildMarkers(null, true),\n\t\t\tsvg = L.Path.SVG,\n\t\t\tm, i, leg, legPath, legLength, nonAnimatable;\n\n\t\tgroup._ignoreMove = true;\n\t\tgroup._animationStart();\n\n\t\t//Make us visible and bring the child markers back in\n\t\tthis.setOpacity(1);\n\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\tm = childMarkers[i];\n\n\t\t\t//Marker was added to us after we were spiderfied\n\t\t\tif (!m._preSpiderfyLatlng) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\t//Close any popup on the marker first, otherwise setting the location of the marker will make the map scroll\n\t\t\tm.closePopup();\n\n\t\t\t//Fix up the location to the real one\n\t\t\tm.setLatLng(m._preSpiderfyLatlng);\n\t\t\tdelete m._preSpiderfyLatlng;\n\n\t\t\t//Hack override the location to be our center\n\t\t\tnonAnimatable = true;\n\t\t\tif (m._setPos) {\n\t\t\t\tm._setPos(thisLayerPos);\n\t\t\t\tnonAnimatable = false;\n\t\t\t}\n\t\t\tif (m.clusterHide) {\n\t\t\t\tm.clusterHide();\n\t\t\t\tnonAnimatable = false;\n\t\t\t}\n\t\t\tif (nonAnimatable) {\n\t\t\t\tfg.removeLayer(m);\n\t\t\t}\n\n\t\t\t// Animate the spider leg back in (animation is actually delegated to CSS transition).\n\t\t\tif (svg) {\n\t\t\t\tleg = m._spiderLeg;\n\t\t\t\tlegPath = leg._path;\n\t\t\t\tlegLength = legPath.getTotalLength() + 0.1;\n\t\t\t\tlegPath.style.strokeDashoffset = legLength;\n\t\t\t\tleg.setStyle({opacity: 0});\n\t\t\t}\n\t\t}\n\n\t\tgroup._ignoreMove = false;\n\n\t\tsetTimeout(function () {\n\t\t\t//If we have only <= one child left then that marker will be shown on the map so don't remove it!\n\t\t\tvar stillThereChildCount = 0;\n\t\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\t\tm = childMarkers[i];\n\t\t\t\tif (m._spiderLeg) {\n\t\t\t\t\tstillThereChildCount++;\n\t\t\t\t}\n\t\t\t}\n\n\n\t\t\tfor (i = childMarkers.length - 1; i >= 0; i--) {\n\t\t\t\tm = childMarkers[i];\n\n\t\t\t\tif (!m._spiderLeg) { //Has already been unspiderfied\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (m.clusterShow) {\n\t\t\t\t\tm.clusterShow();\n\t\t\t\t}\n\t\t\t\tif (m.setZIndexOffset) {\n\t\t\t\t\tm.setZIndexOffset(0);\n\t\t\t\t}\n\n\t\t\t\tif (stillThereChildCount > 1) {\n\t\t\t\t\tfg.removeLayer(m);\n\t\t\t\t}\n\n\t\t\t\tmap.removeLayer(m._spiderLeg);\n\t\t\t\tdelete m._spiderLeg;\n\t\t\t}\n\t\t\tgroup._animationEnd();\n\t\t\tgroup.fire('unspiderfied', {\n\t\t\t\tcluster: me,\n\t\t\t\tmarkers: childMarkers\n\t\t\t});\n\t\t}, 200);\n\t}\n});\n\n\nL.MarkerClusterGroup.include({\n\t//The MarkerCluster currently spiderfied (if any)\n\t_spiderfied: null,\n\n\tunspiderfy: function () {\n\t\tthis._unspiderfy.apply(this, arguments);\n\t},\n\n\t_spiderfierOnAdd: function () {\n\t\tthis._map.on('click', this._unspiderfyWrapper, this);\n\n\t\tif (this._map.options.zoomAnimation) {\n\t\t\tthis._map.on('zoomstart', this._unspiderfyZoomStart, this);\n\t\t}\n\t\t//Browsers without zoomAnimation or a big zoom don't fire zoomstart\n\t\tthis._map.on('zoomend', this._noanimationUnspiderfy, this);\n\n\t\tif (!L.Browser.touch) {\n\t\t\tthis._map.getRenderer(this);\n\t\t\t//Needs to happen in the pageload, not after, or animations don't work in webkit\n\t\t\t//  http://stackoverflow.com/questions/8455200/svg-animate-with-dynamically-added-elements\n\t\t\t//Disable on touch browsers as the animation messes up on a touch zoom and isn't very noticable\n\t\t}\n\t},\n\n\t_spiderfierOnRemove: function () {\n\t\tthis._map.off('click', this._unspiderfyWrapper, this);\n\t\tthis._map.off('zoomstart', this._unspiderfyZoomStart, this);\n\t\tthis._map.off('zoomanim', this._unspiderfyZoomAnim, this);\n\t\tthis._map.off('zoomend', this._noanimationUnspiderfy, this);\n\n\t\t//Ensure that markers are back where they should be\n\t\t// Use no animation to avoid a sticky leaflet-cluster-anim class on mapPane\n\t\tthis._noanimationUnspiderfy();\n\t},\n\n\t//On zoom start we add a zoomanim handler so that we are guaranteed to be last (after markers are animated)\n\t//This means we can define the animation they do rather than Markers doing an animation to their actual location\n\t_unspiderfyZoomStart: function () {\n\t\tif (!this._map) { //May have been removed from the map by a zoomEnd handler\n\t\t\treturn;\n\t\t}\n\n\t\tthis._map.on('zoomanim', this._unspiderfyZoomAnim, this);\n\t},\n\n\t_unspiderfyZoomAnim: function (zoomDetails) {\n\t\t//Wait until the first zoomanim after the user has finished touch-zooming before running the animation\n\t\tif (L.DomUtil.hasClass(this._map._mapPane, 'leaflet-touching')) {\n\t\t\treturn;\n\t\t}\n\n\t\tthis._map.off('zoomanim', this._unspiderfyZoomAnim, this);\n\t\tthis._unspiderfy(zoomDetails);\n\t},\n\n\t_unspiderfyWrapper: function () {\n\t\t/// <summary>_unspiderfy but passes no arguments</summary>\n\t\tthis._unspiderfy();\n\t},\n\n\t_unspiderfy: function (zoomDetails) {\n\t\tif (this._spiderfied) {\n\t\t\tthis._spiderfied.unspiderfy(zoomDetails);\n\t\t}\n\t},\n\n\t_noanimationUnspiderfy: function () {\n\t\tif (this._spiderfied) {\n\t\t\tthis._spiderfied._noanimationUnspiderfy();\n\t\t}\n\t},\n\n\t//If the given layer is currently being spiderfied then we unspiderfy it so it isn't on the map anymore etc\n\t_unspiderfyLayer: function (layer) {\n\t\tif (layer._spiderLeg) {\n\t\t\tthis._featureGroup.removeLayer(layer);\n\n\t\t\tif (layer.clusterShow) {\n\t\t\t\tlayer.clusterShow();\n\t\t\t}\n\t\t\t\t//Position will be fixed up immediately in _animationUnspiderfy\n\t\t\tif (layer.setZIndexOffset) {\n\t\t\t\tlayer.setZIndexOffset(0);\n\t\t\t}\n\n\t\t\tthis._map.removeLayer(layer._spiderLeg);\n\t\t\tdelete layer._spiderLeg;\n\t\t}\n\t}\n});\n", "/**\n * Adds 1 public method to MC<PERSON> and 1 to <PERSON><PERSON> to facilitate changing\n * markers' icon options and refreshing their icon and their parent clusters\n * accordingly (case where their iconCreateFunction uses data of childMarkers\n * to make up the cluster icon).\n */\n\n\nL.MarkerClusterGroup.include({\n\t/**\n\t * Updates the icon of all clusters which are parents of the given marker(s).\n\t * In singleMarkerMode, also updates the given marker(s) icon.\n\t * @param layers L.MarkerClusterGroup|L.LayerGroup|Array(L.Marker)|Map(L.Marker)|\n\t * L.MarkerCluster|L.Marker (optional) list of markers (or single marker) whose parent\n\t * clusters need to be updated. If not provided, retrieves all child markers of this.\n\t * @returns {L.MarkerClusterGroup}\n\t */\n\trefreshClusters: function (layers) {\n\t\tif (!layers) {\n\t\t\tlayers = this._topClusterLevel.getAllChildMarkers();\n\t\t} else if (layers instanceof L.MarkerClusterGroup) {\n\t\t\tlayers = layers._topClusterLevel.getAllChildMarkers();\n\t\t} else if (layers instanceof L.LayerGroup) {\n\t\t\tlayers = layers._layers;\n\t\t} else if (layers instanceof L.MarkerCluster) {\n\t\t\tlayers = layers.getAllChildMarkers();\n\t\t} else if (layers instanceof L.Marker) {\n\t\t\tlayers = [layers];\n\t\t} // else: must be an Array(L.Marker)|Map(L.Marker)\n\t\tthis._flagParentsIconsNeedUpdate(layers);\n\t\tthis._refreshClustersIcons();\n\n\t\t// In case of singleMarkerMode, also re-draw the markers.\n\t\tif (this.options.singleMarkerMode) {\n\t\t\tthis._refreshSingleMarkerModeMarkers(layers);\n\t\t}\n\n\t\treturn this;\n\t},\n\n\t/**\n\t * Simply flags all parent clusters of the given markers as having a \"dirty\" icon.\n\t * @param layers Array(L.Marker)|Map(L.Marker) list of markers.\n\t * @private\n\t */\n\t_flagParentsIconsNeedUpdate: function (layers) {\n\t\tvar id, parent;\n\n\t\t// Assumes layers is an Array or an Object whose prototype is non-enumerable.\n\t\tfor (id in layers) {\n\t\t\t// Flag parent clusters' icon as \"dirty\", all the way up.\n\t\t\t// Dumb process that flags multiple times upper parents, but still\n\t\t\t// much more efficient than trying to be smart and make short lists,\n\t\t\t// at least in the case of a hierarchy following a power law:\n\t\t\t// http://jsperf.com/flag-nodes-in-power-hierarchy/2\n\t\t\tparent = layers[id].__parent;\n\t\t\twhile (parent) {\n\t\t\t\tparent._iconNeedsUpdate = true;\n\t\t\t\tparent = parent.__parent;\n\t\t\t}\n\t\t}\n\t},\n\n\t/**\n\t * Re-draws the icon of the supplied markers.\n\t * To be used in singleMarkerMode only.\n\t * @param layers Array(L.Marker)|Map(L.Marker) list of markers.\n\t * @private\n\t */\n\t_refreshSingleMarkerModeMarkers: function (layers) {\n\t\tvar id, layer;\n\n\t\tfor (id in layers) {\n\t\t\tlayer = layers[id];\n\n\t\t\t// Make sure we do not override markers that do not belong to THIS group.\n\t\t\tif (this.hasLayer(layer)) {\n\t\t\t\t// Need to re-create the icon first, then re-draw the marker.\n\t\t\t\tlayer.setIcon(this._overrideMarkerIcon(layer));\n\t\t\t}\n\t\t}\n\t}\n});\n\nL.Marker.include({\n\t/**\n\t * Updates the given options in the marker's icon and refreshes the marker.\n\t * @param options map object of icon options.\n\t * @param directlyRefreshClusters boolean (optional) true to trigger\n\t * MCG.refreshClustersOf() right away with this single marker.\n\t * @returns {L.Marker}\n\t */\n\trefreshIconOptions: function (options, directlyRefreshClusters) {\n\t\tvar icon = this.options.icon;\n\n\t\tL.setOptions(icon, options);\n\n\t\tthis.setIcon(icon);\n\n\t\t// Shortcut to refresh the associated MCG clusters right away.\n\t\t// To be used when refreshing a single marker.\n\t\t// Otherwise, better use MCG.refreshClusters() once at the end with\n\t\t// the list of modified markers.\n\t\tif (directlyRefreshClusters && this.__parent) {\n\t\t\tthis.__parent._group.refreshClusters(this);\n\t\t}\n\n\t\treturn this;\n\t}\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;CAAA;CACA;CACA;;AAEA,AAAU,KAAC,kBAAkB,GAAG,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC;;CAE7E,CAAC,OAAO,EAAE;CACV,EAAE,gBAAgB,EAAE,EAAE;CACtB,EAAE,kBAAkB,EAAE,IAAI;CAC1B,EAAE,WAAW,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI;;CAE9C,EAAE,mBAAmB,EAAE,KAAK;CAC5B,EAAE,iBAAiB,EAAE,IAAI;CACzB,EAAE,mBAAmB,EAAE,IAAI;CAC3B,EAAE,mBAAmB,EAAE,IAAI;CAC3B,EAAE,gBAAgB,EAAE,KAAK;;CAEzB,EAAE,uBAAuB,EAAE,IAAI;;CAE/B;CACA;CACA,EAAE,0BAA0B,EAAE,IAAI;;CAElC;CACA;CACA;CACA,EAAE,OAAO,EAAE,IAAI;;CAEf;CACA;CACA,EAAE,oBAAoB,EAAE,KAAK;;CAE7B;CACA,EAAE,sBAAsB,EAAE,IAAI;;CAE9B;CACA,EAAE,0BAA0B,EAAE,CAAC;;CAE/B;CACA,EAAE,wBAAwB,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE;;CAExE;CACA,EAAE,cAAc,EAAE,KAAK;CACvB,EAAE,aAAa,EAAE,GAAG;CACpB,EAAE,UAAU,EAAE,EAAE;CAChB,EAAE,aAAa,EAAE,IAAI;;CAErB;CACA,EAAE,cAAc,EAAE,EAAE;CACpB,EAAE;;CAEF,CAAC,UAAU,EAAE,UAAU,OAAO,EAAE;CAChC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;CACnC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;CACxC,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC;CACrE,GAAG;;CAEH,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;CACxC,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;CAE1C,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,YAAY,EAAE,CAAC;CACzC,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;CAE3C,EAAE,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;CAC5B,EAAE,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;CAC3B;CACA,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;;CAElC,EAAE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;;CAEnB,EAAE,IAAI,CAAC,yBAAyB,GAAG;CACnC,GAAG,WAAW,EAAE,IAAI,CAAC,qBAAqB;CAC1C,GAAG,MAAM,EAAE,IAAI,CAAC,iBAAiB;CACjC,GAAG,SAAS,EAAE,IAAI,CAAC,mBAAmB;CACtC,GAAG,CAAC;;CAEJ;CACA,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;CAC7D,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;CACpE;CACA,EAAE,IAAI,CAAC,cAAc,GAAG,OAAO,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,wBAAwB,CAAC;CAC/E,EAAE;;CAEF,CAAC,QAAQ,EAAE,UAAU,KAAK,EAAE;;CAE5B,EAAE,IAAI,KAAK,YAAY,CAAC,CAAC,UAAU,EAAE;CACrC,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;CAClC,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;CACxB,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CACvC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;CAC3C,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACrC,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;CAC3C,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;CAC5B,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;;CAGH;;CAEA,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;CACxB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;CACtB,GAAG;;CAEH,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;CACvC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;;CAE1C;CACA,EAAE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;;CAE7C,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;;CAE/B;CACA,EAAE,IAAI,YAAY,GAAG,KAAK;CAC1B,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;CAC/B,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE;CACtB,GAAG,OAAO,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI,WAAW,EAAE;CACtD,IAAI,YAAY,GAAG,YAAY,CAAC,QAAQ,CAAC;CACzC,IAAI;CACJ,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE;CACnE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE;CAC1C,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;CACjD,IAAI,MAAM;CACV,IAAI,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;CAC5D,IAAI;CACJ,GAAG;CACH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF,CAAC,WAAW,EAAE,UAAU,KAAK,EAAE;;CAE/B,EAAE,IAAI,KAAK,YAAY,CAAC,CAAC,UAAU,EAAE;CACrC,GAAG,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;CACrC,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;CACxB,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CAC1C,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;CAC9C,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;CACjF,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;CACtE,IAAI;CACJ,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;CAC9C,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;CACvB,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;CACxB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;CACtB,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;CAChC,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;CACjC,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;;CAE7C;CACA,EAAE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;;CAE7C,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;;CAE/B,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;;CAElD,EAAE,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;CAC1C,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACzC,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE;CAC1B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;CACxB,IAAI;CACJ,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,UAAU,WAAW,EAAE,iBAAiB,EAAE;CACtD,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;CACpC,GAAG,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;CACrC,GAAG;;CAEH,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,aAAa;CAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc;CAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;CAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;CAChD,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;CAChD,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM;CAC5B,MAAM,MAAM,GAAG,CAAC;CAChB,MAAM,aAAa,GAAG,IAAI;CAC1B,MAAM,CAAC,CAAC;;CAER,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;CACjB,GAAG,IAAI,OAAO,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC;CACxC,GAAG,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY;CACpC,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC;;CAEvC;CACA,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;CACvC,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;CACxB,KAAK;;CAEL,IAAI,OAAO,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE;CACjC,KAAK,IAAI,OAAO,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,EAAE;CACxC;CACA,MAAM,IAAI,OAAO,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;CACnD,MAAM,IAAI,OAAO,GAAG,aAAa,EAAE;CACnC,OAAO,MAAM;CACb,OAAO;CACP,MAAM;;CAEN,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;;CAE7B;CACA;CACA;CACA;CACA;CACA;CACA,KAAK,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE;CACpC,MAAM,IAAI,aAAa,EAAE;CACzB,OAAO,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;CACzC,OAAO,aAAa,GAAG,KAAK,CAAC;CAC7B,OAAO;CACP,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;CAClD,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC7B,MAAM,SAAS;CACf,MAAM;;CAEN;CACA,KAAK,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE;CACvB,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACtB,MAAM,IAAI,CAAC,iBAAiB,EAAE;CAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3C,OAAO;CACP,MAAM,SAAS;CACf,MAAM;;CAEN,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;CAC3B,MAAM,SAAS;CACf,MAAM;;CAEN,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;CACtC,KAAK,IAAI,CAAC,iBAAiB,EAAE;CAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;CAC1C,MAAM;;CAEN;CACA,KAAK,IAAI,CAAC,CAAC,QAAQ,EAAE;CACrB,MAAM,IAAI,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,KAAK,CAAC,EAAE;CAC5C,OAAO,IAAI,OAAO,GAAG,CAAC,CAAC,QAAQ,CAAC,kBAAkB,EAAE;CACpD,WAAW,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACpE,OAAO,EAAE,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;CACnC,OAAO;CACP,MAAM;CACN,KAAK;;CAEL,IAAI,IAAI,aAAa,EAAE;CACvB;CACA,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,CAAC;CAChE,KAAK;;CAEL;CACA,IAAI,IAAI,MAAM,KAAK,CAAC,EAAE;;CAEtB;CACA,KAAK,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;;CAEhD,KAAK,IAAI,CAAC,qBAAqB,EAAE,CAAC;;CAElC,KAAK,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;CACpG,KAAK,MAAM;CACX,KAAK,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;CAClD,KAAK;CACL,IAAI,EAAE,IAAI,CAAC,CAAC;;CAEZ,GAAG,OAAO,EAAE,CAAC;CACb,GAAG,MAAM;CACT,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC;;CAE/C,GAAG,OAAO,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,EAAE;CAChC,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC;;CAE5B;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE;CACnC,KAAK,IAAI,aAAa,EAAE;CACxB,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;CACxC,MAAM,aAAa,GAAG,KAAK,CAAC;CAC5B,MAAM;CACN,KAAK,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;CACjD,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC5B,KAAK,SAAS;CACd,KAAK;;CAEL;CACA,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE;CACtB,KAAK,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACrB,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;CAC1B,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAC5B,IAAI;CACJ,GAAG;CACH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF;CACA,CAAC,YAAY,EAAE,UAAU,WAAW,EAAE;CACtC,EAAE,IAAI,CAAC,EAAE,CAAC;CACV,MAAM,CAAC,GAAG,WAAW,CAAC,MAAM;CAC5B,MAAM,EAAE,GAAG,IAAI,CAAC,aAAa;CAC7B,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc;CAC/B,MAAM,aAAa,GAAG,IAAI,CAAC;;CAE3B,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC3B,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;;CAEvB;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE;CACnC,KAAK,IAAI,aAAa,EAAE;CACxB,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;CACxC,MAAM,aAAa,GAAG,KAAK,CAAC;CAC5B,MAAM;CACN,KAAK,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;CACjD,KAAK,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC5B,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;CAChD,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACvB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;CAC1B,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;CAC/D,KAAK;CACL,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3C,IAAI;CACJ,GAAG,OAAO,IAAI,CAAC;CACf,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;CACxB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;CAEtB;CACA,GAAG,IAAI,YAAY,GAAG,WAAW,CAAC,KAAK,EAAE;CACzC,OAAO,EAAE,GAAG,CAAC,CAAC;CACd,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;CAC5B,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAExB;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE;CACnC,KAAK,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;CAClD,KAAK,EAAE,GAAG,YAAY,CAAC,MAAM,CAAC;CAC9B,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;CAC7B,IAAI;CACJ,GAAG;;CAEH,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC1B,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;;CAEtB;CACA,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,UAAU,EAAE;CAClC,IAAI,IAAI,aAAa,EAAE;CACvB,KAAK,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC;CACvC,KAAK,aAAa,GAAG,KAAK,CAAC;CAC3B,KAAK;CACL,IAAI,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;CAChD,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC3B,IAAI,SAAS;CACb,IAAI;;CAEJ,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,EAAE;CACpB,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACvB,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;CAC3C,IAAI,SAAS;CACb,IAAI;;CAEJ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;CACpC,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;;CAE1C,GAAG,IAAI,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;CACvB,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACtB,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE;CACvB,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK;CACL,IAAI;CACJ,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;;CAE7C,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;;CAE/B;CACA,EAAE,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC;;CAEjG,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF;CACA,CAAC,WAAW,EAAE,YAAY;CAC1B;;CAEA;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;CAC9B,GAAG,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;CAC5B,GAAG,OAAO,IAAI,CAAC,aAAa,CAAC;CAC7B,GAAG,OAAO,IAAI,CAAC,gBAAgB,CAAC;CAChC,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,sBAAsB,EAAE;CACnC,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;CACjC,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;;CAEpC,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,EAAE;CACnC,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;CACpD,GAAG,OAAO,MAAM,CAAC,QAAQ,CAAC;CAC1B,GAAG,EAAE,IAAI,CAAC,CAAC;;CAEX,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;CACjB;CACA,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;CACnC,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,YAAY;CACxB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;;CAEpC,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;CAChD,GAAG;;CAEH,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9D,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;CACvD,GAAG;;CAEH,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,CAAC;;CAEjD,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,UAAU,MAAM,EAAE,OAAO,EAAE;CACvC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;CAC7C,GAAG,aAAa,GAAG,IAAI,CAAC,cAAc;CACtC,GAAG,iBAAiB,EAAE,CAAC,EAAE,CAAC,CAAC;;CAE3B,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;CACrD,GAAG;;CAEH,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC5C,GAAG,iBAAiB,GAAG,IAAI,CAAC;;CAE5B,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,IAAI,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;CAC/C,KAAK,iBAAiB,GAAG,KAAK,CAAC;CAC/B,KAAK,MAAM;CACX,KAAK;CACL,IAAI;;CAEJ,GAAG,IAAI,iBAAiB,EAAE;CAC1B,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CACrC,IAAI;CACJ,GAAG;;CAEH,EAAE,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;CACjD,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,YAAY;CACxB,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC;CAClB,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;CAC9B,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAClB,GAAG,CAAC,CAAC;CACL,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF;CACA,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;CACzB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC;;CAEpB,EAAE,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;CAExB,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;CAC9B,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;CAC1B,IAAI,MAAM,GAAG,CAAC,CAAC;CACf,IAAI;CACJ,GAAG,CAAC,CAAC;;CAEL,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF;CACA,CAAC,QAAQ,EAAE,UAAU,KAAK,EAAE;CAC5B,EAAE,IAAI,CAAC,KAAK,EAAE;CACd,GAAG,OAAO,KAAK,CAAC;CAChB,GAAG;;CAEH,EAAE,IAAI,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC;;CAEzC,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC5C,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,KAAK,EAAE;CAC7B,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI;CACJ,GAAG;;CAEH,EAAE,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;CAChC,EAAE,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC5C,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;CACnC,IAAI,OAAO,KAAK,CAAC;CACjB,IAAI;CACJ,GAAG;;CAEH,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CACrG,EAAE;;CAEF;CACA,CAAC,eAAe,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;;CAE7C,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;;CAEtB,EAAE,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;CACtC,GAAG,QAAQ,GAAG,YAAY,EAAE,CAAC;CAC7B,GAAG;;CAEH,EAAE,IAAI,UAAU,GAAG,YAAY;CAC/B;CACA;CACA,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE;CACxF,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC/C,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;;CAE/C,IAAI,IAAI,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;CAC7B,KAAK,QAAQ,EAAE,CAAC;CAChB,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE;CACrC,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;CAC7C,KAAK,KAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;CAC/B,KAAK;CACL,IAAI;CACJ,GAAG,CAAC;;CAEJ,EAAE,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,EAAE;CACxE;CACA,GAAG,QAAQ,EAAE,CAAC;CACd,GAAG,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;CACjE;CACA,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC7C,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;CACtC,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC7C,GAAG,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;CAC7C,GAAG,KAAK,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;CACjC,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE;CACvB,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;CAClB,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC;;CAElB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE;CACzC,GAAG,MAAM,8BAA8B,CAAC;CACxC,GAAG;;CAEH,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;CAChC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;CAEjC,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;CAC3B,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;CACnC,GAAG;;CAEH,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC;;CAEzD;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC1D,GAAG,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;CAClC,GAAG,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC;CACzC,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC;CACtC,GAAG;CACH;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC1D,GAAG,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;CAClC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;CACxC,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC;CACzC,GAAG;CACH,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;;CAE3B;CACA,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC3C,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;;CAE9D,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;CAC/C,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;;CAE/C,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;CAC3B,GAAG;;CAEH,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;;CAErB;CACA,EAAE,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;CAC5B,EAAE,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;CAC7B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;CAC1B,EAAE;;CAEF;CACA,CAAC,QAAQ,EAAE,UAAU,GAAG,EAAE;CAC1B,EAAE,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;CAC1C,EAAE,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;;CAE1C,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;;CAEvB;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;;CAEnG,EAAE,IAAI,IAAI,CAAC,mBAAmB,EAAE;CAChC,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;CAC9B,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC;;CAEtB;CACA,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;CACvB,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;CAC9B,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;;CAE/B,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;;CAEnC,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;CACnB,EAAE;;CAEF,CAAC,gBAAgB,EAAE,UAAU,MAAM,EAAE;CACrC,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC;CACvB,EAAE,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;CACpC,GAAG,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC;CAC9B,GAAG;CACH,EAAE,OAAO,OAAO,IAAI,IAAI,CAAC;CACzB,EAAE;;CAEF;CACA,CAAC,YAAY,EAAE,UAAU,OAAO,EAAE,GAAG,EAAE;CACvC,EAAE,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAChD,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;CAC3B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;CACzB,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,0BAA0B,EAAE,UAAU,MAAM,EAAE,CAAC,EAAE;CAClD,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI;CACrB,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;CAC7C,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;CAEhD,EAAE,OAAO,CAAC,IAAI,OAAO,EAAE,CAAC,EAAE,EAAE;CAC5B,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE;CACrF,IAAI,MAAM;CACV,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF,CAAC,qBAAqB,EAAE,UAAU,CAAC,EAAE;CACrC,EAAE,CAAC,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;CAC1C,EAAE;;CAEF,CAAC,iBAAiB,EAAE,UAAU,CAAC,EAAE;CACjC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE;CAClD,GAAG,IAAI,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;;CAEjE,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;;CAEpD,GAAG,IAAI,WAAW,EAAE;CACpB,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;CACzB,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF,CAAC,UAAU,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE;CACxC,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;CACvB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;CAE1B,EAAE,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;CACrB,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CACvB,EAAE;;CAEF,CAAC,mBAAmB,EAAE,UAAU,CAAC,EAAE;CACnC,EAAE,IAAI,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;CACvC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC;CAC9B,EAAE,IAAI,SAAS,EAAE;CACjB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;CAC1D,GAAG;CACH,EAAE;;;CAGF;CACA;CACA,CAAC,YAAY,EAAE,UAAU,MAAM,EAAE,sBAAsB,EAAE,aAAa,EAAE;CACxE,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa;CACvC,GAAG,eAAe,GAAG,IAAI,CAAC,gBAAgB;CAC1C,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa;CAC1B,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI;CAClB,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;CAEhD;CACA,EAAE,IAAI,sBAAsB,EAAE;CAC9B,GAAG,IAAI,CAAC,0BAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;CAC1D,GAAG;;CAEH;CACA,EAAE,IAAI,OAAO,GAAG,MAAM,CAAC,QAAQ;CAC/B,GAAG,OAAO,GAAG,OAAO,CAAC,QAAQ;CAC7B,GAAG,WAAW,CAAC;;CAEf;CACA,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;;CAErC,EAAE,OAAO,OAAO,EAAE;CAClB,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;CACzB,GAAG,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;;CAEpC,GAAG,IAAI,OAAO,CAAC,KAAK,GAAG,OAAO,EAAE;CAChC;CACA,IAAI,MAAM;CACV,IAAI,MAAM,IAAI,sBAAsB,IAAI,OAAO,CAAC,WAAW,IAAI,CAAC,EAAE;CAClE;CACA,IAAI,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;CAE7F;CACA,IAAI,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;CACpG,IAAI,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;CAE/G;CACA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;CAChE,IAAI,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;CAChD,IAAI,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;;CAE5C,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;CACvB;CACA,KAAK,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;CAC7B,KAAK,IAAI,CAAC,aAAa,EAAE;CACzB,MAAM,EAAE,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;CAC/B,MAAM;CACN,KAAK;CACL,IAAI,MAAM;CACV,IAAI,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC;CACpC,IAAI;;CAEJ,GAAG,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC;CAC9B,GAAG;;CAEH,EAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;CACzB,EAAE;;CAEF,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE;CACnC,EAAE,OAAO,GAAG,EAAE;CACd,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE;CACnB,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI;CACJ,GAAG,GAAG,GAAG,GAAG,CAAC,UAAU,CAAC;CACxB,GAAG;CACH,EAAE,OAAO,KAAK,CAAC;CACf,EAAE;;CAEF;CACA,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;CACxC,EAAE,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,YAAY,CAAC,CAAC,aAAa,EAAE;CACrD;CACA,GAAG,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;CACrG,IAAI,OAAO;CACX,IAAI;CACJ,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI,CAAC;CAC3B,GAAG;;CAEH,EAAE,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;CAClE,EAAE;;CAEF;CACA,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,SAAS,EAAE;CACrC,EAAE,OAAO,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC;CAClJ,EAAE;;CAEF;CACA,CAAC,0BAA0B,EAAE,UAAU,OAAO,EAAE;CAChD,EAAE,IAAI,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;;CAE3C,EAAE,IAAI,CAAC,GAAG,kBAAkB,CAAC;CAC7B,EAAE,IAAI,UAAU,GAAG,EAAE,EAAE;CACvB,GAAG,CAAC,IAAI,OAAO,CAAC;CAChB,GAAG,MAAM,IAAI,UAAU,GAAG,GAAG,EAAE;CAC/B,GAAG,CAAC,IAAI,QAAQ,CAAC;CACjB,GAAG,MAAM;CACT,GAAG,CAAC,IAAI,OAAO,CAAC;CAChB,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,aAAa,GAAG,UAAU,GAAG,eAAe,EAAE,SAAS,EAAE,gBAAgB,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;CAC/I,EAAE;;CAEF,CAAC,WAAW,EAAE,YAAY;CAC1B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI;CACrB,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB;CACxD,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;CAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;CAC5D,MAAM,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC;;CAE7D;CACA,EAAE,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,mBAAmB,EAAE;CACvE,GAAG,IAAI,CAAC,EAAE,CAAC,8BAA8B,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;CACvE,GAAG;;CAEH;CACA,EAAE,IAAI,mBAAmB,EAAE;CAC3B,GAAG,IAAI,CAAC,EAAE,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CACzD,GAAG,IAAI,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CACxD,GAAG,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CAC/C,GAAG;CACH,EAAE;;CAEF,CAAC,eAAe,EAAE,UAAU,CAAC,EAAE;CAC/B,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC,KAAK;CACvB,MAAM,aAAa,GAAG,OAAO,CAAC;;CAE9B,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,iBAAiB,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,aAAa,CAAC,OAAO,KAAK,EAAE,EAAE;CACzF,GAAG,OAAO;CACV,GAAG;;CAEH,EAAE,OAAO,aAAa,CAAC,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;CACpD,GAAG,aAAa,GAAG,aAAa,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;CACnD,GAAG;;CAEH,EAAE,IAAI,aAAa,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ;CAC3C,GAAG,aAAa,CAAC,WAAW,KAAK,OAAO,CAAC,WAAW;CACpD,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;;CAEnC;CACA,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;CACtB,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;CAC/C,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;CAC1B,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;CACxC,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;CACtB,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,aAAa,CAAC,OAAO,KAAK,EAAE,EAAE;CACzD,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;CAChC,GAAG;CACH,EAAE;;CAEF,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE;CAC7B,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;CACtB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,OAAO;CACV,GAAG;CACH,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;CAC1B,GAAG,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CACvC,GAAG;CACH,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,EAAE;CACnE,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,aAAa,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;CAC5F,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CACpC,GAAG;CACH,EAAE;;CAEF,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;CAC1B,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CAC7C,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;CAC7B,GAAG;CACH,EAAE;;CAEF,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,IAAI,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB;CACxD,GAAG,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;CACzD,GAAG,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;CACzD,GAAG,mBAAmB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB;CACzD,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;;CAEnB,EAAE,IAAI,iBAAiB,IAAI,mBAAmB,IAAI,mBAAmB,EAAE;CACvE,GAAG,IAAI,CAAC,GAAG,CAAC,8BAA8B,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;CACxE,GAAG;CACH,EAAE,IAAI,mBAAmB,EAAE;CAC3B,GAAG,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CAC1D,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CACzD,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;CAChD,GAAG;CACH,EAAE;;CAEF,CAAC,QAAQ,EAAE,YAAY;CACvB,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,OAAO;CACV,GAAG;CACH,EAAE,IAAI,CAAC,mBAAmB,EAAE,CAAC;;CAE7B,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC3C,EAAE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;CAC9D,EAAE;;CAEF,CAAC,QAAQ,EAAE,YAAY;CACvB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,OAAO;CACV,GAAG;;CAEH,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAC;;CAEnD,EAAE,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;CAC/I,EAAE,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,SAAS,CAAC,CAAC;;CAEnG,EAAE,IAAI,CAAC,mBAAmB,GAAG,SAAS,CAAC;CACvC,EAAE,OAAO;CACT,EAAE;;CAEF,CAAC,wBAAwB,EAAE,YAAY;CACvC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;CACjD,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;CAC/C,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB;CACzC,GAAG,QAAQ,GAAG,MAAM,CAAC;;CAErB;CACA;CACA;CACA,EAAE,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;CACpC,GAAG,QAAQ,GAAG,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;CAC7C,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,KAAK,IAAI,EAAE;CACrD,GAAG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC;CACtD,GAAG;CACH,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;CAC1B,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;CAC1B,EAAE,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;;CAE7B;CACA,EAAE,KAAK,IAAI,IAAI,GAAG,OAAO,EAAE,IAAI,IAAI,OAAO,EAAE,IAAI,EAAE,EAAE;CACpD,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;CACjE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;CACpE,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;CACrE,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE;CACnC,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,aAAa;CACvC,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;CAC7C,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;CAC/C,MAAM,WAAW,EAAE,CAAC,CAAC;;CAErB,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;CACrC,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;CACnC,GAAG;;CAEH,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAC;;CAEjD;CACA,EAAE,OAAO,IAAI,IAAI,OAAO,EAAE,IAAI,EAAE,EAAE;CAClC,GAAG,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,IAAI,CAAC,CAAC;;CAE5D;CACA,GAAG,IAAI,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;CAC/D,GAAG,IAAI,OAAO,EAAE;CAChB,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;CAC7B,IAAI,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;CAC7B,IAAI,OAAO;CACX,IAAI;;CAEJ;CACA,GAAG,OAAO,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;CAC9D,GAAG,IAAI,OAAO,EAAE;CAChB,IAAI,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;CAClC,IAAI,IAAI,MAAM,EAAE;CAChB,KAAK,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;CACvC,KAAK;;CAEL;;CAEA,IAAI,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;CACzE,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC;CAC3F,IAAI,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC;CAClC,IAAI,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;;CAEhC;CACA,IAAI,IAAI,UAAU,GAAG,UAAU,CAAC;CAChC,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE;CAC9C,KAAK,UAAU,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;CAC/D,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;CACtF,KAAK;CACL,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;;CAEjC;CACA,IAAI,IAAI,CAAC,0BAA0B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;;CAEnD,IAAI,OAAO;CACX,IAAI;;CAEJ;CACA,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;CACvD,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;CACzC,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC;CACzC,EAAE,OAAO;CACT,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA,CAAC,qBAAqB,EAAE,YAAY;CACpC,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;CAC5C,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,aAAa,IAAI,CAAC,CAAC,gBAAgB,EAAE;CAC3D,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;CACpB,IAAI;CACJ,GAAG,CAAC,CAAC;CACL,EAAE;;CAEF;CACA,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE;CACzB,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CACvB,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;CAC3B,GAAG,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;CAC1E,GAAG;CACH,EAAE;CACF,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC/C,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC7B,GAAG;CACH,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;CACzB,EAAE,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CACnC,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;CAC5B,EAAE;;CAEF;CACA,CAAC,mBAAmB,EAAE,YAAY;CAClC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;CAE5C;CACA,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;;CAEvB,EAAE,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,yBAAyB,EAAE,CAAC,EAAE;CACrG,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;CAC1B;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;;CAEvK,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;;CAE9C,GAAG,MAAM,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,EAAE;CACnC,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;;CAE1B,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;CAC/C,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;CACnB,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,yBAAyB,EAAE,YAAY;CACxC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,0BAA0B,EAAE;CAChD,GAAG,OAAO,IAAI,CAAC,kBAAkB,CAAC;CAClC,GAAG,MAAM,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;CAC/B,GAAG,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;CACzD,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAC/D,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,kBAAkB,EAAE,UAAU,MAAM,EAAE;CACvC,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;;CAE5B,EAAE,IAAI,MAAM,KAAK,SAAS,EAAE;CAC5B,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,EAAE;CACpC,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;CACrC,IAAI;CACJ,GAAG,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE;CACrC,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;CACtC,IAAI;CACJ,GAAG;;CAEH,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF;CACA,CAAC,6BAA6B,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;CAC7D,EAAE,IAAI,UAAU,KAAK,KAAK,EAAE;CAC5B,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CACtC,GAAG,MAAM,IAAI,UAAU,CAAC,WAAW,KAAK,CAAC,EAAE;CAC3C,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;;CAE1B,GAAG,IAAI,OAAO,GAAG,UAAU,CAAC,kBAAkB,EAAE,CAAC;CACjD,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9C,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;CAC9C,GAAG,MAAM;CACT,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC;CAC5B,GAAG;CACH,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,sBAAsB,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE;CAClD,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,SAAS,EAAE;CAChC,MAAM,CAAC,GAAG,CAAC;CACX,MAAM,KAAK,CAAC;;CAEZ,EAAE,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;;CAExB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACjC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;CAErB,GAAG,IAAI,KAAK,YAAY,CAAC,CAAC,UAAU,EAAE;CACtC,IAAI,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;CAC/C,IAAI,SAAS;CACb,IAAI;;CAEJ,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CACtB,GAAG;;CAEH,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,mBAAmB,EAAE,UAAU,KAAK,EAAE;CACvC,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;CAClE,GAAG,aAAa,EAAE,YAAY;CAC9B,IAAI,OAAO,CAAC,CAAC;CACb,IAAI;CACJ,GAAG,kBAAkB,EAAE,YAAY;CACnC,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC;CACnB,IAAI;CACJ,GAAG,CAAC,CAAC;;CAEL,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;CACF,CAAC,CAAC,CAAC;;CAEH;CACA,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;CAC7B,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;CAC7G,CAAC,CAAC,CAAC;;CAEH,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;CAC7B,CAAC,YAAY,EAAE;CACf;CACA,EAAE,eAAe,EAAE,YAAY;CAC/B;CACA,GAAG;CACH,EAAE,gBAAgB,EAAE,UAAU,iBAAiB,EAAE,YAAY,EAAE;CAC/D,GAAG,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC;CAC5I,GAAG,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;;CAE5G;CACA,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;CAC7B,GAAG;CACH,EAAE,iBAAiB,EAAE,UAAU,iBAAiB,EAAE,YAAY,EAAE;CAChE,GAAG,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,iBAAiB,CAAC,CAAC;CAC5I,GAAG,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;;CAE5G;CACA,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;CAC7B,GAAG;CACH,EAAE,kBAAkB,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;CACnD,GAAG,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;CACzD,GAAG;CACH,EAAE;;CAEF,CAAC,cAAc,EAAE;CACjB;CACA,EAAE,eAAe,EAAE,YAAY;CAC/B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,uBAAuB,CAAC;CAC3D,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;CAC3B,GAAG;;CAEH,EAAE,gBAAgB,EAAE,UAAU,iBAAiB,EAAE,YAAY,EAAE;CAC/D,GAAG,IAAI,MAAM,GAAG,IAAI,CAAC,yBAAyB,EAAE;CAChD,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa;CAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;CAChD,OAAO,CAAC,CAAC;;CAET,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;;CAE3B;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE;CACvF,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAC,OAAO;CAC5B,QAAQ,OAAO,IAAI,CAAC,CAAC,QAAQ;CAC7B,QAAQ,CAAC,CAAC;;CAEV,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;CACpC,KAAK,QAAQ,GAAG,IAAI,CAAC;CACrB,KAAK;;CAEL,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,IAAI,iBAAiB,GAAG,CAAC,KAAK,YAAY,EAAE;CACvE,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACvB,KAAK,CAAC,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;CAChE,KAAK,MAAM;CACX;CACA,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK,CAAC,CAAC,4BAA4B,CAAC,QAAQ,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;CACpE,KAAK;;CAEL;CACA;CACA,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9C,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACpB,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;CACtC,MAAM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACxB,MAAM;CACN,KAAK;;CAEL,IAAI,CAAC,CAAC;;CAEN,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;;CAEvB;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;CACzE;CACA,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;CAC7B,IAAI,IAAI,EAAE,CAAC,YAAY,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE;CACpD,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK;CACL,IAAI,CAAC,CAAC;;CAEN;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;CAC5F,IAAI,CAAC,CAAC,iCAAiC,CAAC,YAAY,CAAC,CAAC;CACtD,IAAI,CAAC,CAAC;;CAEN,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;;CAE5B;CACA,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY;CAC7B;CACA,IAAI,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,iBAAiB,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE;CACxF,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACvB,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK,CAAC,CAAC;;CAEP,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;CACzB,IAAI,CAAC,CAAC;CACN,GAAG;;CAEH,EAAE,iBAAiB,EAAE,UAAU,iBAAiB,EAAE,YAAY,EAAE;CAChE,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,EAAE,iBAAiB,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;;CAE5F;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,4BAA4B,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;CAC5G;CACA,GAAG,IAAI,CAAC,gBAAgB,CAAC,iCAAiC,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,EAAE,iBAAiB,EAAE,IAAI,CAAC,yBAAyB,EAAE,CAAC,CAAC;CAC9K,GAAG;;CAEH,EAAE,kBAAkB,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;CACnD,GAAG,IAAI,EAAE,GAAG,IAAI;CAChB,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC;;CAE/B,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;CACtB,GAAG,IAAI,UAAU,KAAK,KAAK,EAAE;CAC7B,IAAI,IAAI,UAAU,CAAC,WAAW,GAAG,CAAC,EAAE;;CAEpC,KAAK,UAAU,CAAC,WAAW,EAAE,CAAC;CAC9B,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;CACzB,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;;CAE5B,KAAK,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;CACzE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC;;CAEzB,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY;CAC/B,MAAM,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CAC5B,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;;CAE1B,MAAM,EAAE,CAAC,aAAa,EAAE,CAAC;CACzB,MAAM,CAAC,CAAC;;CAER,KAAK,MAAM;CACX,KAAK,IAAI,CAAC,YAAY,EAAE,CAAC;;CAEzB,KAAK,EAAE,CAAC,eAAe,EAAE,CAAC;CAC1B,KAAK,EAAE,CAAC,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;CAChF,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,uBAAuB,EAAE,UAAU,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE;CAC9E,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,yBAAyB,EAAE;CAC/C,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;CAEhD;CACA,EAAE,OAAO,CAAC,4CAA4C,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC;;CAE7G,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;;CAEhB;CACA,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;CACtB,EAAE,OAAO,CAAC,yBAAyB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;;CAE1D;CACA;CACA,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY;;CAE5B;CACA,GAAG,IAAI,OAAO,CAAC,WAAW,KAAK,CAAC,EAAE;CAClC,IAAI,IAAI,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CAChC;CACA,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;CAC5B,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;CAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;CAC7B,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE;CACvB,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK;CACL,IAAI,MAAM;CACV,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,CAAC,EAAE;CACrE,KAAK,CAAC,CAAC,iCAAiC,CAAC,MAAM,EAAE,OAAO,EAAE,iBAAiB,GAAG,CAAC,CAAC,CAAC;CACjF,KAAK,CAAC,CAAC;CACP,IAAI;CACJ,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;CACtB,GAAG,CAAC,CAAC;CACL,EAAE;;CAEF,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE;CACjB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;CACpG,GAAG;CACH,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC;CAC1B,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;CAC5B,EAAE;;CAEF;CACA;CACA,CAAC,YAAY,EAAE,YAAY;CAC3B;CACA;;CAEA,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;CAC5C,EAAE;CACF,CAAC,CAAC,CAAC;;CAEH,CAAC,CAAC,kBAAkB,GAAG,UAAU,OAAO,EAAE;CAC1C,CAAC,OAAO,IAAI,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;CAC1C,CAAC,CAAC;;ACr3CQ,KAAC,aAAa,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;CAC7D,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;;CAElC,CAAC,UAAU,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE;;CAE1C,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;CACjG,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;;CAE7D,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;CACtB,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;;CAEpB,EAAE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;CACrB,EAAE,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;CAC3B,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;CACvB,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC/B,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;;CAEhC,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;;CAEtC,EAAE,IAAI,CAAC,EAAE;CACT,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;CACrB,GAAG;CACH,EAAE,IAAI,CAAC,EAAE;CACT,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;CACrB,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,kBAAkB,EAAE,UAAU,YAAY,EAAE,mBAAmB,EAAE;CAClE,EAAE,YAAY,GAAG,YAAY,IAAI,EAAE,CAAC;;CAEpC,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC5D,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,YAAY,EAAE,mBAAmB,CAAC,CAAC;CAChF,GAAG;;CAEH,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACtD,GAAG,IAAI,mBAAmB,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE;CAC5D,IAAI,SAAS;CACb,IAAI;CACJ,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;CACvC,GAAG;;CAEH,EAAE,OAAO,YAAY,CAAC;CACtB,EAAE;;CAEF;CACA,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;CAC1B,EAAE;;CAEF;CACA,CAAC,YAAY,EAAE,UAAU,gBAAgB,EAAE;CAC3C,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE;CACjD,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;CACzB,GAAG,UAAU,GAAG,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;CAC/C,GAAG,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;CACxB,GAAG,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE;CAC1B,GAAG,CAAC,CAAC;;CAEL;CACA,EAAE,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,GAAG,IAAI,EAAE;CACxD,GAAG,IAAI,EAAE,CAAC;CACV,GAAG,IAAI,WAAW,GAAG,EAAE,CAAC;CACxB,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC9C,IAAI,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;CACtE,IAAI;CACJ,GAAG,aAAa,GAAG,WAAW,CAAC;CAC/B,GAAG;;CAEH,EAAE,IAAI,UAAU,GAAG,IAAI,EAAE;CACzB,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;CAChD,GAAG,MAAM,IAAI,UAAU,IAAI,OAAO,EAAE;CACpC,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;CACvD,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;CAC9D,GAAG;CACH,EAAE;;CAEF,CAAC,SAAS,EAAE,YAAY;CACxB,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,YAAY,EAAE,CAAC;CACpC,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CAC9B,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;;CAEF,CAAC,WAAW,EAAE,YAAY;CAC1B,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;CAC/B,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;CACtB,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,UAAU,EAAE,YAAY;CACzB,EAAE,IAAI,IAAI,CAAC,gBAAgB,EAAE;CAC7B,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;CAChE,GAAG,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;CACjC,GAAG;CACH,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;CACpC,EAAE;CACF,CAAC,YAAY,EAAE,YAAY;CAC3B,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC;CACtC,EAAE;;;CAGF,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE,uBAAuB,EAAE;;CAErD,EAAE,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;;CAE/B,EAAE,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;CAChC,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;;CAE/B,EAAE,IAAI,IAAI,YAAY,CAAC,CAAC,aAAa,EAAE;CACvC,GAAG,IAAI,CAAC,uBAAuB,EAAE;CACjC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACnC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;CACzB,IAAI;CACJ,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC;CACxC,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,uBAAuB,EAAE;CACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC7B,IAAI;CACJ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;CACtB,GAAG;;CAEH,EAAE,IAAI,IAAI,CAAC,QAAQ,EAAE;CACrB,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;CACvC,GAAG;CACH,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA,CAAC,iBAAiB,EAAE,UAAU,KAAK,EAAE;CACrC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;CACtB;CACA,GAAG,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC;CACnD,GAAG;CACH,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,YAAY,EAAE,YAAY;CAC3B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;;CAE5B,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE;CACzB,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;CACpC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC;CACpC,GAAG;CACH,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE;CACzB,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;CACrC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,GAAG,CAAC,QAAQ,CAAC;CACrC,GAAG;CACH,EAAE;;CAEF,CAAC,kBAAkB,EAAE,YAAY;CACjC,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ;CAC7B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc;CACzC,MAAM,MAAM,GAAG,CAAC;CAChB,MAAM,MAAM,GAAG,CAAC;CAChB,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;CACnC,MAAM,CAAC,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC;;CAExC;CACA,EAAE,IAAI,UAAU,KAAK,CAAC,EAAE;CACxB,GAAG,OAAO;CACV,GAAG;;CAEH;CACA,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;;CAEtB;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACvC,GAAG,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;;CAEpC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;;CAEpC,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC;CAC7B,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC;CAC7B,GAAG;;CAEH;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC7C,GAAG,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;;CAE5B;CACA,GAAG,IAAI,KAAK,CAAC,iBAAiB,EAAE;CAChC,IAAI,KAAK,CAAC,kBAAkB,EAAE,CAAC;CAC/B,IAAI;;CAEJ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;;CAEtC,GAAG,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC;CAChC,GAAG,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC;;CAElC,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC;CAC1C,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG,GAAG,UAAU,CAAC;CAC1C,GAAG;;CAEH,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC;;CAExF;CACA,EAAE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;CACjC,EAAE;;CAEF;CACA,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;CAChC,EAAE,IAAI,QAAQ,EAAE;CAChB,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC;CACrC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;CAC5B,GAAG;CACH,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC3C,EAAE;;CAEF,CAAC,6BAA6B,EAAE,UAAU,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;CACnE,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,GAAG,CAAC;CACtE,GAAG,UAAU,CAAC,EAAE;CAChB,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,QAAQ;CAC5B,KAAK,CAAC,EAAE,CAAC,CAAC;CACV,IAAI,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9C,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;;CAEpB;CACA,KAAK,IAAI,CAAC,CAAC,KAAK,EAAE;CAClB,MAAM,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACxB,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;CACtB,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG,UAAU,CAAC,EAAE;CAChB,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,cAAc;CACxC,KAAK,CAAC,EAAE,EAAE,CAAC;CACX,IAAI,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACpD,KAAK,EAAE,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CAC3B,KAAK,IAAI,EAAE,CAAC,KAAK,EAAE;CACnB,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACzB,MAAM,EAAE,CAAC,WAAW,EAAE,CAAC;CACvB,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;;CAEF,CAAC,4CAA4C,EAAE,UAAU,MAAM,EAAE,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAE;CAC9G,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU;CACpD,GAAG,UAAU,CAAC,EAAE;CAChB,IAAI,CAAC,CAAC,6BAA6B,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,iBAAiB,CAAC,CAAC;;CAExH;CACA;CACA,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE,IAAI,iBAAiB,GAAG,CAAC,KAAK,YAAY,EAAE;CACvE,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK,CAAC,CAAC,iCAAiC,CAAC,MAAM,EAAE,UAAU,EAAE,iBAAiB,CAAC,CAAC;CAChF,KAAK,MAAM;CACX,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK;;CAEL,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC;CAClB,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;;CAEF,CAAC,yBAAyB,EAAE,UAAU,MAAM,EAAE,SAAS,EAAE;CACzD,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE;CACzF,GAAG,CAAC,CAAC,WAAW,EAAE,CAAC;CACnB,GAAG,CAAC,CAAC;CACL,EAAE;;CAEF,CAAC,4BAA4B,EAAE,UAAU,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE;CACtE,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAAE,SAAS;CACxE,GAAG,UAAU,CAAC,EAAE;CAChB,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,KAAK,EAAE;CAC/B,KAAK,OAAO;CACZ,KAAK;;CAEL;CACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACrD,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;CAE5B,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;CACvC,MAAM,SAAS;CACf,MAAM;;CAEN,KAAK,IAAI,QAAQ,EAAE;CACnB,MAAM,EAAE,CAAC,aAAa,GAAG,EAAE,CAAC,SAAS,EAAE,CAAC;;CAExC,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;CAC7B,MAAM,IAAI,EAAE,CAAC,WAAW,EAAE;CAC1B,OAAO,EAAE,CAAC,WAAW,EAAE,CAAC;CACxB,OAAO;CACP,MAAM;;CAEN,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;CACzC,KAAK;CACL,IAAI;CACJ,GAAG,UAAU,CAAC,EAAE;CAChB,IAAI,CAAC,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;CAC1B,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;;CAEF,CAAC,iCAAiC,EAAE,UAAU,SAAS,EAAE;CACzD;CACA,EAAE,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACtD,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CAC7B,GAAG,IAAI,EAAE,CAAC,aAAa,EAAE;CACzB,IAAI,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC;CACnC,IAAI,OAAO,EAAE,CAAC,aAAa,CAAC;CAC5B,IAAI;CACJ,GAAG;;CAEH,EAAE,IAAI,SAAS,GAAG,CAAC,KAAK,IAAI,CAAC,KAAK,EAAE;CACpC;CACA,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,gBAAgB,EAAE,CAAC;CAC9C,IAAI;CACJ,GAAG,MAAM;CACT,GAAG,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC7D,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,iCAAiC,CAAC,SAAS,CAAC,CAAC;CACxE,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF,CAAC,gBAAgB,EAAE,YAAY;CAC/B,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;CAC1B,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;CACtC,GAAG,OAAO,IAAI,CAAC,aAAa,CAAC;CAC7B,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,iCAAiC,EAAE,UAAU,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE;CACnG,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;CACX,EAAE,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,UAAU,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC;CACjE,GAAG,UAAU,CAAC,EAAE;CAChB;CACA,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACjD,KAAK,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CACvB,KAAK,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;CAC7D,MAAM,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC5C,MAAM,IAAI,CAAC,CAAC,WAAW,EAAE;CACzB,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;CACvB,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG,UAAU,CAAC,EAAE;CAChB;CACA,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACvD,KAAK,CAAC,GAAG,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;CAC7B,KAAK,IAAI,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;CAC7D,MAAM,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CAC5C,MAAM,IAAI,CAAC,CAAC,WAAW,EAAE;CACzB,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;CACvB,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG,CAAC;CACJ,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,YAAY,EAAE,UAAU,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE;CAChH,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,cAAc;CACzC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK;CACvB,MAAM,CAAC,EAAE,CAAC,CAAC;;CAEX,EAAE,IAAI,gBAAgB,IAAI,IAAI,EAAE;CAChC,GAAG,IAAI,eAAe,EAAE;CACxB,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;CAC1B,IAAI;CACJ,GAAG,IAAI,gBAAgB,IAAI,IAAI,KAAK,eAAe,EAAE;CACrD,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;CAC3B,IAAI;CACJ,GAAG;;CAEH,EAAE,IAAI,IAAI,GAAG,gBAAgB,IAAI,IAAI,GAAG,eAAe,EAAE;CACzD,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACnD,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;CACzB,IAAI,IAAI,CAAC,CAAC,iBAAiB,EAAE;CAC7B,KAAK,CAAC,CAAC,kBAAkB,EAAE,CAAC;CAC5B,KAAK;CACL,IAAI,IAAI,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE;CAC/C,KAAK,CAAC,CAAC,YAAY,CAAC,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,CAAC,CAAC;CAC3G,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,eAAe,EAAE,YAAY;CAC9B;CACA,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC;CACnG,EAAE;CACF,CAAC,CAAC;;CCpZF;CACA;CACA;CACA;CACA;CACA;CACA;;CAEA,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;CACjB,CAAC,WAAW,EAAE,YAAY;CAC1B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;CACpC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CACrB,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;CAChC,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;CACF;CACA,CAAC,WAAW,EAAE,YAAY;CAC1B,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;CAC/C,EAAE;CACF,CAAC,CAAC,CAAC;;CClBH,CAAC,CAAC,YAAY,GAAG,UAAU,QAAQ,EAAE;CACrC,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;CAC3B,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,GAAG,QAAQ,CAAC;CACxC,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;CACjB,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;CACzB,CAAC,CAAC;;CAEF,CAAC,CAAC,YAAY,CAAC,SAAS,GAAG;;CAE3B,CAAC,SAAS,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;CAClC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK;CACvB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;CACnC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;CAClC,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;;CAEhC,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;;CAEnC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACjB,EAAE;;CAEF,CAAC,YAAY,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;CACrC,EAAE,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;CACzB,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;CAC7B,EAAE;;CAEF;CACA,CAAC,YAAY,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;CACrC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK;CACvB,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE;CACnC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE;CAClC,MAAM,CAAC,EAAE,GAAG,CAAC;;CAEb,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;;CAE9C,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CAC/C,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;;CAExB,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;CAEtB,IAAI,IAAI,GAAG,KAAK,CAAC,EAAE;CACnB,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;CACnB,KAAK;;CAEL,IAAI,OAAO,IAAI,CAAC;CAChB,IAAI;CACJ,GAAG;;CAEH,EAAE;;CAEF,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE,OAAO,EAAE;CACpC,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO;CACtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;;CAExB,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE;CAClB,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;;CAEjB,GAAG,KAAK,CAAC,IAAI,GAAG,EAAE;CAClB,IAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;;CAElB,IAAI,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CACjD,KAAK,OAAO,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CACzC,KAAK,IAAI,OAAO,EAAE;CAClB,MAAM,CAAC,EAAE,CAAC;CACV,MAAM,GAAG,EAAE,CAAC;CACZ,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE;CACjC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACjC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI;CACxC,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY;CACrC,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW;CACtC,MAAM,OAAO,GAAG,IAAI,CAAC;;CAErB,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACnC,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;CACvB,GAAG,IAAI,GAAG,EAAE;;CAEZ,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CACrC,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;CACnB,KAAK,IAAI,IAAI,EAAE;;CAEf,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CACnD,OAAO,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CACrB,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CAClE,OAAO,IAAI,IAAI,GAAG,aAAa;CAC/B,QAAQ,IAAI,IAAI,aAAa,IAAI,OAAO,KAAK,IAAI,EAAE;CACnD,QAAQ,aAAa,GAAG,IAAI,CAAC;CAC7B,QAAQ,OAAO,GAAG,GAAG,CAAC;CACtB,QAAQ;CACR,OAAO;CACP,MAAM;CACN,KAAK;CACL,IAAI;CACJ,GAAG;CACH,EAAE,OAAO,OAAO,CAAC;CACjB,EAAE;;CAEF,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE;CACzB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;CAC7C,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;CACrC,EAAE;;CAEF,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE;CAC3B,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CACrB,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACtB,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;CAC3B,EAAE;CACF,CAAC,CAAC;;CCrHF;CACA;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;;CAEA;CACA;;CAEA;CACA;CACA;CACA;CACA;CACA;CACA;;CAEA;CACA;;CAEA,CAAC,YAAY;CACb,CAAC,CAAC,CAAC,SAAS,GAAG;;CAEf;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,UAAU,EAAE,UAAU,GAAG,EAAE,EAAE,EAAE;CACjC,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG;CACjC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;CAC/B,GAAG,QAAQ,EAAE,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;CACpE,GAAG;;CAEH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,gCAAgC,EAAE,UAAU,QAAQ,EAAE,OAAO,EAAE;CACjE,GAAG,IAAI,IAAI,GAAG,CAAC;CACf,IAAI,KAAK,GAAG,IAAI;CAChB,IAAI,SAAS,GAAG,EAAE;CAClB,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;;CAEb,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC7C,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACpB,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;;CAEtC,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE;CACf,KAAK,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;CACxB,KAAK,MAAM;CACX,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,IAAI,CAAC,GAAG,IAAI,EAAE;CAClB,KAAK,IAAI,GAAG,CAAC,CAAC;CACd,KAAK,KAAK,GAAG,EAAE,CAAC;CAChB,KAAK;CACL,IAAI;;CAEJ,GAAG,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;CACpD,GAAG;;;CAGH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,eAAe,EAAE,UAAU,QAAQ,EAAE,OAAO,EAAE;CAChD,GAAG,IAAI,mBAAmB,GAAG,EAAE;CAC/B,IAAI,CAAC,GAAG,IAAI,CAAC,gCAAgC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;;CAEjE,GAAG,IAAI,CAAC,CAAC,QAAQ,EAAE;CACnB,IAAI,mBAAmB;CACvB,KAAK,mBAAmB,CAAC,MAAM;CAC/B,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC;CAClE,MAAM,CAAC;CACP,IAAI,mBAAmB;CACvB,KAAK,mBAAmB,CAAC,MAAM;CAC/B,MAAM,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC;CAClE,MAAM,CAAC;CACP,IAAI,OAAO,mBAAmB,CAAC;CAC/B,IAAI,MAAM;CACV,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;CACzB,IAAI;CACJ,GAAG;;CAEH;CACA;CACA;CACA;CACA;CACA;CACA;CACA,EAAE,aAAa,EAAE,UAAU,OAAO,EAAE;CACpC;CACA,GAAG,IAAI,MAAM,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK;CACrC,IAAI,MAAM,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK;CAClC,IAAI,QAAQ,GAAG,IAAI,EAAE,QAAQ,GAAG,IAAI;CACpC,IAAI,QAAQ,GAAG,IAAI,EAAE,QAAQ,GAAG,IAAI;CACpC,IAAI,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI;CAC9B,IAAI,CAAC,CAAC;;CAEN,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC7C,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CACxB,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE;CAC7C,KAAK,QAAQ,GAAG,EAAE,CAAC;CACnB,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;CACrB,KAAK;CACL,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE;CAC7C,KAAK,QAAQ,GAAG,EAAE,CAAC;CACnB,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;CACrB,KAAK;CACL,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE;CAC7C,KAAK,QAAQ,GAAG,EAAE,CAAC;CACnB,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;CACrB,KAAK;CACL,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,GAAG,MAAM,EAAE;CAC7C,KAAK,QAAQ,GAAG,EAAE,CAAC;CACnB,KAAK,MAAM,GAAG,EAAE,CAAC,GAAG,CAAC;CACrB,KAAK;CACL,IAAI;CACJ;CACA,GAAG,IAAI,MAAM,KAAK,MAAM,EAAE;CAC1B,IAAI,KAAK,GAAG,QAAQ,CAAC;CACrB,IAAI,KAAK,GAAG,QAAQ,CAAC;CACrB,IAAI,MAAM;CACV,IAAI,KAAK,GAAG,QAAQ,CAAC;CACrB,IAAI,KAAK,GAAG,QAAQ,CAAC;CACrB,IAAI;;CAEJ,GAAG,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC;CACnE,QAAQ,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;CACvD,GAAG,OAAO,EAAE,CAAC;CACb,GAAG;CACH,EAAE,CAAC;CACH,CAAC,EAAE,EAAE;;CAEL,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;CACxB,CAAC,aAAa,EAAE,YAAY;CAC5B,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,EAAE;CAC9C,GAAG,MAAM,GAAG,EAAE;CACd,GAAG,CAAC,EAAE,CAAC,CAAC;;CAER,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACjD,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC;CACnC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;CAClB,GAAG;;CAEH,EAAE,OAAO,CAAC,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;CAC3C,EAAE;CACF,CAAC,CAAC,CAAC;;CCpKH;CACA;;CAEA,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;;CAExB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;CAClB,CAAC,qBAAqB,EAAE,EAAE;CAC1B,CAAC,iBAAiB,EAAE,CAAC;;CAErB,CAAC,qBAAqB,GAAG,EAAE;CAC3B,CAAC,kBAAkB,EAAE,EAAE;CACvB,CAAC,mBAAmB,EAAE,CAAC;;CAEvB,CAAC,uBAAuB,EAAE,CAAC;CAC3B;;CAEA,CAAC,QAAQ,EAAE,YAAY;CACvB,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;CACxE,GAAG,OAAO;CACV,GAAG;;CAEH,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;CACxD,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM;CACtB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI;CACnB,GAAG,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;CAChD,GAAG,SAAS,CAAC;;CAEb,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;CAC5B,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;;CAEjC;;CAEA,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,EAAE;CAClD,GAAG,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACvF,GAAG,MAAM,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,CAAC,uBAAuB,EAAE;CAClE,GAAG,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACvE,GAAG,MAAM;CACT,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,CAAC;CAClB,GAAG,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CACvE,GAAG;;CAEH,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;CACnD,EAAE;;CAEF,CAAC,UAAU,EAAE,UAAU,WAAW,EAAE;CACpC;CACA,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE;CACpC,GAAG,OAAO;CACV,GAAG;CACH,EAAE,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;;CAEzC,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;CACjC,EAAE;;CAEF,CAAC,qBAAqB,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;CACnD,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B,GAAG,IAAI,CAAC,qBAAqB,IAAI,CAAC,GAAG,KAAK,CAAC;CAC/G,GAAG,SAAS,GAAG,aAAa,GAAG,IAAI,CAAC,IAAI;CACxC,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,KAAK;CAChC,GAAG,GAAG,GAAG,EAAE;CACX,GAAG,CAAC,EAAE,KAAK,CAAC;;CAEZ,EAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;;CAEtC,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;;CAErB,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;CAC9B,GAAG,KAAK,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,GAAG,SAAS,CAAC;CAClD,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;CACrH,GAAG;;CAEH,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;;CAEF,CAAC,qBAAqB,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE;CACnD,EAAE,IAAI,0BAA0B,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,0BAA0B;CACjF,GAAG,SAAS,GAAG,0BAA0B,GAAG,IAAI,CAAC,kBAAkB;CACnE,GAAG,UAAU,GAAG,0BAA0B,GAAG,IAAI,CAAC,qBAAqB;CACvE,GAAG,YAAY,GAAG,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,IAAI;CACnF,GAAG,KAAK,GAAG,CAAC;CACZ,GAAG,GAAG,GAAG,EAAE;CACX,GAAG,CAAC,CAAC;;CAEL,EAAE,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC;;CAErB;CACA,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAC/B;CACA;CACA,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE;CAClB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;CACtH,IAAI;CACJ,GAAG,KAAK,IAAI,UAAU,GAAG,SAAS,GAAG,CAAC,GAAG,MAAM,CAAC;CAChD,GAAG,SAAS,IAAI,YAAY,GAAG,KAAK,CAAC;CACrC,GAAG;CACH,EAAE,OAAO,GAAG,CAAC;CACb,EAAE;;CAEF,CAAC,sBAAsB,EAAE,YAAY;CACrC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM;CACzB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI;CACnB,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa;CAC3B,GAAG,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;CACrD,GAAG,CAAC,EAAE,CAAC,CAAC;;CAER,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;;CAE3B,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CACrB,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACjD,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAEvB,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;;CAErB,GAAG,IAAI,CAAC,CAAC,kBAAkB,EAAE;CAC7B,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;CACtC,IAAI,OAAO,CAAC,CAAC,kBAAkB,CAAC;CAChC,IAAI;CACJ,GAAG,IAAI,CAAC,CAAC,eAAe,EAAE;CAC1B,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;CACzB,IAAI;;CAEJ,GAAG,IAAI,CAAC,CAAC,UAAU,EAAE;CACrB,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;CAClC,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC;CACxB,IAAI;CACJ,GAAG;;CAEH,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE;CAC7B,GAAG,OAAO,EAAE,IAAI;CAChB,GAAG,OAAO,EAAE,YAAY;CACxB,GAAG,CAAC,CAAC;CACL,EAAE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;CAC5B,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;CAC3B,EAAE;CACF,CAAC,CAAC,CAAC;;CAEH;CACA,CAAC,CAAC,wBAAwB,GAAG,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC;CACpD,CAAC,kBAAkB,EAAE,UAAU,YAAY,EAAE,SAAS,EAAE;CACxD,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM;CACzB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI;CACnB,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa;CAC3B,GAAG,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wBAAwB;CAC5D,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,CAAC;;CAErB,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;;CAE3B;CACA;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC5C,GAAG,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;CACjD,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAEvB;CACA,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;CAC5D,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CACrB,GAAG,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC;;CAEtB;CACA,GAAG,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC;CACpC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;CACvB,GAAG,IAAI,CAAC,CAAC,eAAe,EAAE;CAC1B,IAAI,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;CAC/B,IAAI;;CAEJ,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;CAClB,GAAG;CACH,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;CAEvB,EAAE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;CAC5B,EAAE,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;CAC3B,GAAG,OAAO,EAAE,IAAI;CAChB,GAAG,OAAO,EAAE,YAAY;CACxB,GAAG,CAAC,CAAC;CACL,EAAE;;CAEF,CAAC,oBAAoB,EAAE,YAAY;CACnC,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC;CAChC,EAAE;CACF,CAAC,CAAC,CAAC;;CAEH;CACA,CAAC,CAAC,aAAa,CAAC,OAAO,CAAC;;CAExB,CAAC,kBAAkB,EAAE,UAAU,YAAY,EAAE,SAAS,EAAE;CACxD,EAAE,IAAI,EAAE,GAAG,IAAI;CACf,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM;CACtB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI;CACnB,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa;CAC3B,GAAG,eAAe,GAAG,IAAI,CAAC,OAAO;CACjC,GAAG,YAAY,GAAG,GAAG,CAAC,kBAAkB,CAAC,eAAe,CAAC;CACzD,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG;CACnB,GAAG,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,wBAAwB,CAAC;CAC1E,GAAG,eAAe,GAAG,UAAU,CAAC,OAAO;CACvC,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;;CAEzC,EAAE,IAAI,eAAe,KAAK,SAAS,EAAE;CACrC,GAAG,eAAe,GAAG,CAAC,CAAC,kBAAkB,CAAC,SAAS,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC;CAC7F,GAAG;;CAEH,EAAE,IAAI,GAAG,EAAE;CACX;CACA,GAAG,UAAU,CAAC,OAAO,GAAG,CAAC,CAAC;;CAE1B;CACA,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,EAAE,IAAI,6BAA6B,CAAC;CACvF,GAAG,MAAM;CACT;CACA,GAAG,UAAU,CAAC,OAAO,GAAG,eAAe,CAAC;CACxC,GAAG;;CAEH,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;;CAE3B;CACA;CACA;CACA,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC5C,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAEvB,GAAG,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;;CAEjD;CACA,GAAG,GAAG,GAAG,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;CAC/D,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CACrB,GAAG,CAAC,CAAC,UAAU,GAAG,GAAG,CAAC;;CAEtB;CACA;CACA,GAAG,IAAI,GAAG,EAAE;CACZ,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;CACxB,IAAI,SAAS,GAAG,OAAO,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;CAC/C,IAAI,OAAO,CAAC,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC;CAC9C,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;CAC/C,IAAI;;CAEJ;CACA,GAAG,IAAI,CAAC,CAAC,eAAe,EAAE;CAC1B,IAAI,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;CAC/B,IAAI;CACJ,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;CACtB,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;CACpB,IAAI;CACJ;CACA;CACA,GAAG,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;CAElB,GAAG,IAAI,CAAC,CAAC,OAAO,EAAE;CAClB,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;CAC5B,IAAI;CACJ,GAAG;;CAEH,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC;CACvB,EAAE,KAAK,CAAC,eAAe,EAAE,CAAC;;CAE1B;CACA,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACjD,GAAG,MAAM,GAAG,GAAG,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;CACjD,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAEvB;CACA,GAAG,CAAC,CAAC,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC;CACpC,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;CACvB;CACA,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;CACtB,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;CACpB,IAAI;;CAEJ;CACA,GAAG,IAAI,GAAG,EAAE;CACZ,IAAI,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC;CACvB,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;CACxB,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,CAAC;CACvC;CACA,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,CAAC;CAC7C,IAAI;CACJ,GAAG;CACH,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;;CAEvB,EAAE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;;CAE5B,EAAE,UAAU,CAAC,YAAY;CACzB,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;CACzB,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,EAAE;CAC5B,IAAI,OAAO,EAAE,EAAE;CACf,IAAI,OAAO,EAAE,YAAY;CACzB,IAAI,CAAC,CAAC;CACN,GAAG,EAAE,GAAG,CAAC,CAAC;CACV,EAAE;;CAEF,CAAC,oBAAoB,EAAE,UAAU,WAAW,EAAE;CAC9C,EAAE,IAAI,EAAE,GAAG,IAAI;CACf,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM;CACtB,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI;CACnB,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa;CAC3B,GAAG,YAAY,GAAG,WAAW,GAAG,GAAG,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,EAAE,WAAW,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;CACrJ,GAAG,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,IAAI,CAAC;CACrD,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG;CACnB,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;;CAEhD,EAAE,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;CAC3B,EAAE,KAAK,CAAC,eAAe,EAAE,CAAC;;CAE1B;CACA,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CACrB,EAAE,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CACjD,GAAG,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAEvB;CACA,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB,EAAE;CAC9B,IAAI,SAAS;CACb,IAAI;;CAEJ;CACA,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC;;CAElB;CACA,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC;CACrC,GAAG,OAAO,CAAC,CAAC,kBAAkB,CAAC;;CAE/B;CACA,GAAG,aAAa,GAAG,IAAI,CAAC;CACxB,GAAG,IAAI,CAAC,CAAC,OAAO,EAAE;CAClB,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;CAC5B,IAAI,aAAa,GAAG,KAAK,CAAC;CAC1B,IAAI;CACJ,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;CACtB,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;CACpB,IAAI,aAAa,GAAG,KAAK,CAAC;CAC1B,IAAI;CACJ,GAAG,IAAI,aAAa,EAAE;CACtB,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACtB,IAAI;;CAEJ;CACA,GAAG,IAAI,GAAG,EAAE;CACZ,IAAI,GAAG,GAAG,CAAC,CAAC,UAAU,CAAC;CACvB,IAAI,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;CACxB,IAAI,SAAS,GAAG,OAAO,CAAC,cAAc,EAAE,GAAG,GAAG,CAAC;CAC/C,IAAI,OAAO,CAAC,KAAK,CAAC,gBAAgB,GAAG,SAAS,CAAC;CAC/C,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;CAC/B,IAAI;CACJ,GAAG;;CAEH,EAAE,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;;CAE5B,EAAE,UAAU,CAAC,YAAY;CACzB;CACA,GAAG,IAAI,oBAAoB,GAAG,CAAC,CAAC;CAChC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAClD,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;CACxB,IAAI,IAAI,CAAC,CAAC,UAAU,EAAE;CACtB,KAAK,oBAAoB,EAAE,CAAC;CAC5B,KAAK;CACL,IAAI;;;CAGJ,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;CAClD,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;;CAExB,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE;CACvB,KAAK,SAAS;CACd,KAAK;;CAEL,IAAI,IAAI,CAAC,CAAC,WAAW,EAAE;CACvB,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;CACrB,KAAK;CACL,IAAI,IAAI,CAAC,CAAC,eAAe,EAAE;CAC3B,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;CAC1B,KAAK;;CAEL,IAAI,IAAI,oBAAoB,GAAG,CAAC,EAAE;CAClC,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;CACvB,KAAK;;CAEL,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;CAClC,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC;CACxB,IAAI;CACJ,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;CACzB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE;CAC9B,IAAI,OAAO,EAAE,EAAE;CACf,IAAI,OAAO,EAAE,YAAY;CACzB,IAAI,CAAC,CAAC;CACN,GAAG,EAAE,GAAG,CAAC,CAAC;CACV,EAAE;CACF,CAAC,CAAC,CAAC;;;CAGH,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;CAC7B;CACA,CAAC,WAAW,EAAE,IAAI;;CAElB,CAAC,UAAU,EAAE,YAAY;CACzB,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;CAC1C,EAAE;;CAEF,CAAC,gBAAgB,EAAE,YAAY;CAC/B,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;;CAEvD,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE;CACvC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;CAC9D,GAAG;CACH;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;;CAE7D,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE;CACxB,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;CAC/B;CACA;CACA;CACA,GAAG;CACH,EAAE;;CAEF,CAAC,mBAAmB,EAAE,YAAY;CAClC,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;CACxD,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;CAC9D,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;CAC5D,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;;CAE9D;CACA;CACA,EAAE,IAAI,CAAC,sBAAsB,EAAE,CAAC;CAChC,EAAE;;CAEF;CACA;CACA,CAAC,oBAAoB,EAAE,YAAY;CACnC,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,OAAO;CACV,GAAG;;CAEH,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;CAC3D,EAAE;;CAEF,CAAC,mBAAmB,EAAE,UAAU,WAAW,EAAE;CAC7C;CACA,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,kBAAkB,CAAC,EAAE;CAClE,GAAG,OAAO;CACV,GAAG;;CAEH,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;CAC5D,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;CAChC,EAAE;;CAEF,CAAC,kBAAkB,EAAE,YAAY;CACjC;CACA,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;CACrB,EAAE;;CAEF,CAAC,WAAW,EAAE,UAAU,WAAW,EAAE;CACrC,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;CACxB,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;CAC5C,GAAG;CACH,EAAE;;CAEF,CAAC,sBAAsB,EAAE,YAAY;CACrC,EAAE,IAAI,IAAI,CAAC,WAAW,EAAE;CACxB,GAAG,IAAI,CAAC,WAAW,CAAC,sBAAsB,EAAE,CAAC;CAC7C,GAAG;CACH,EAAE;;CAEF;CACA,CAAC,gBAAgB,EAAE,UAAU,KAAK,EAAE;CACpC,EAAE,IAAI,KAAK,CAAC,UAAU,EAAE;CACxB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;;CAEzC,GAAG,IAAI,KAAK,CAAC,WAAW,EAAE;CAC1B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;CACxB,IAAI;CACJ;CACA,GAAG,IAAI,KAAK,CAAC,eAAe,EAAE;CAC9B,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;CAC7B,IAAI;;CAEJ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;CAC3C,GAAG,OAAO,KAAK,CAAC,UAAU,CAAC;CAC3B,GAAG;CACH,EAAE;CACF,CAAC,CAAC,CAAC;;CC5dH;CACA;CACA;CACA;CACA;CACA;;;CAGA,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;CAC7B;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,eAAe,EAAE,UAAU,MAAM,EAAE;CACpC,EAAE,IAAI,CAAC,MAAM,EAAE;CACf,GAAG,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;CACvD,GAAG,MAAM,IAAI,MAAM,YAAY,CAAC,CAAC,kBAAkB,EAAE;CACrD,GAAG,MAAM,GAAG,MAAM,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;CACzD,GAAG,MAAM,IAAI,MAAM,YAAY,CAAC,CAAC,UAAU,EAAE;CAC7C,GAAG,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC;CAC3B,GAAG,MAAM,IAAI,MAAM,YAAY,CAAC,CAAC,aAAa,EAAE;CAChD,GAAG,MAAM,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;CACxC,GAAG,MAAM,IAAI,MAAM,YAAY,CAAC,CAAC,MAAM,EAAE;CACzC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;CACrB,GAAG;CACH,EAAE,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,CAAC;CAC3C,EAAE,IAAI,CAAC,qBAAqB,EAAE,CAAC;;CAE/B;CACA,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,EAAE;CACrC,GAAG,IAAI,CAAC,+BAA+B,CAAC,MAAM,CAAC,CAAC;CAChD,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA,CAAC,2BAA2B,EAAE,UAAU,MAAM,EAAE;CAChD,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC;;CAEjB;CACA,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE;CACrB;CACA;CACA;CACA;CACA;CACA,GAAG,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC;CAChC,GAAG,OAAO,MAAM,EAAE;CAClB,IAAI,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC;CACnC,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC;CAC7B,IAAI;CACJ,GAAG;CACH,EAAE;;CAEF;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,+BAA+B,EAAE,UAAU,MAAM,EAAE;CACpD,EAAE,IAAI,EAAE,EAAE,KAAK,CAAC;;CAEhB,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE;CACrB,GAAG,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;;CAEtB;CACA,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;CAC7B;CACA,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;CACnD,IAAI;CACJ,GAAG;CACH,EAAE;CACF,CAAC,CAAC,CAAC;;CAEH,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;CACjB;CACA;CACA;CACA;CACA;CACA;CACA;CACA,CAAC,kBAAkB,EAAE,UAAU,OAAO,EAAE,uBAAuB,EAAE;CACjE,EAAE,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;;CAE/B,EAAE,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;CAE9B,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;;CAErB;CACA;CACA;CACA;CACA,EAAE,IAAI,uBAAuB,IAAI,IAAI,CAAC,QAAQ,EAAE;CAChD,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;CAC9C,GAAG;;CAEH,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;CACF,CAAC,CAAC,CAAC;;;;;;;;;;;;;"}