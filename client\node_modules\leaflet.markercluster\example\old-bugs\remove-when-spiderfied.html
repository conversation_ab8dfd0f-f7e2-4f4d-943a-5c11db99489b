<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">Remove and add direct to map</button><button id="doit2">clearLayers</button><button id="doit3">removeLayers</button><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/54">#54</a>. Spiderfy the cluster then click the button. Should result in 2 markers right beside each other on the map.</span><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/53">#53</a>. Spiderfy the cluster then click the button. Spider lines remain on the map.</span><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/49">#49</a>. Spiderfy the cluster then click the second button. Spider lines remain on the map. Click the map to get an error.</span>

	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(50.5, 30.51);

		var map = new L.Map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = new L.MarkerClusterGroup();
		var markersList = [];
		var m;

		m = new L.Marker([50.5, 30.51]);
		markersList.push(m);
		markers.addLayer(m);
		m = new L.Marker([50.5, 30.5101]);
		markersList.push(m);
		markers.addLayer(m);

		map.addLayer(markers);

		//Ugly add/remove code
		L.DomUtil.get('doit').onclick = function () {
			map.removeLayer(markers);
			map.addLayer(markersList[0]);
			map.addLayer(markersList[1]);
		};
		L.DomUtil.get('doit2').onclick = function () {
			markers.clearLayers();
		};
		L.DomUtil.get('doit3').onclick = function () {
			markers.removeLayers(markersList);
		};

	</script>
</body>
</html>
