<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<p>Whenever a marker is clicked it is removed from the clusterer and added directly to the map instead.</p>
	<p>Click Marker on Left, zoom out 1 layer, click marker on right.</p>
	<p>Expected behaviour: Both markers are shown. Bugged behaviour: Both markers are on map with opacity 0.</p>
	<pre id="log"></pre>

	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(50.5, 30.51);

		var map = new L.Map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = new L.MarkerClusterGroup({ animateAddingMarkers: true });
		var markersList = [];
		var m;

		m = new L.Marker([50.5, 30.51]);
		markersList.push(m);
		markers.addLayer(m);
		m = new L.Marker([50.5, 30.515]);
		markersList.push(m);
		markers.addLayer(m);

		map.addLayer(markers);

		var lastClicked = null;
		markers.on('click', function (m) {
			console.log('clicked ' + m);
			if (lastClicked) {
				map.removeLayer(lastClicked);
				markers.addLayer(lastClicked);
			}

			lastClicked = m.layer;

			markers.removeLayer(lastClicked);
			map.addLayer(lastClicked);
		});

		map.on('click', function () {
			console.log('map clicked');
			if (lastClicked) {
				map.removeLayer(lastClicked);
				markers.addLayer(lastClicked);
			}
			lastClicked = null;
		});
	</script>
</body>
</html>
