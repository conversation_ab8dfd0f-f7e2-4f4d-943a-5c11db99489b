<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="screen.css" />

	<link rel="stylesheet" href="../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../dist/MarkerCluster.Default.css" />
	<script src="../dist/leaflet.markercluster-src.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="populate">Populate 1 marker</button>
	<button id="remove">Remove 1 marker</button>

	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = L.latLng(50.5, 30.51);

		var map = L.map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = L.markerClusterGroup({ spiderfyOnMaxZoom: false, showCoverageOnHover: false, zoomToBoundsOnClick: false });

		function populate() {
			for (var i = 0; i < 100; i++) {
				var m = L.marker(getRandomLatLng(map));
				markers.addLayer(m);
			}
			return false;
		}
		function getRandomLatLng(map) {
			var bounds = map.getBounds(),
				southWest = bounds.getSouthWest(),
				northEast = bounds.getNorthEast(),
				lngSpan = northEast.lng - southWest.lng,
				latSpan = northEast.lat - southWest.lat;

			return L.latLng(
					southWest.lat + latSpan * Math.random(),
					southWest.lng + lngSpan * Math.random());
		}


		var polygon;
		markers.on('clustermouseover', function (a) {
			if (polygon) {
				map.removeLayer(polygon);
			}
			polygon = L.polygon(a.layer.getConvexHull());
			map.addLayer(polygon);
		});

		markers.on('clustermouseout', function (a) {
			if (polygon) {
				map.removeLayer(polygon);
				polygon = null;
			}
		});

		map.on('zoomend', function () {
			if (polygon) {
				map.removeLayer(polygon);
				polygon = null;
			}
		});


		populate();
		map.addLayer(markers);
	</script>
</body>
</html>
