<template>
  <div style="height: 90vh; width: 100%; position: relative">
    <div class="layer-control">
      <div class="p-3 bg-white rounded shadow">
        <!-- Performance info -->
        <div class="mb-3 text-sm text-gray-600">
          <div>Zoom: {{ zoom }}</div>
          <div>Visible Points: {{ visiblePointsCount }}</div>
        </div>

        <!-- Iterate over layers to create checkboxes -->
        <div
          v-for="layer in layers"
          :key="layer.id"
          class="flex items-center gap-2 mb-2"
        >
          <XCheckbox
            v-model="layer.visible"
            :input-id="layer.id"
            binary
            @click="checkboxClicked"
          />
          <label :for="layer.id" class="ml-1">
            {{ layer.name }}
            <span v-if="layer.totalFeatures" class="text-xs text-gray-500">
              ({{ layer.totalFeatures }} total)
            </span>
          </label>
        </div>
      </div>
    </div>

    <l-map
      ref="map"
      v-model:zoom="zoom"
      :center="center"
      @update:zoom="onZoomChange"
      @update:bounds="onBoundsChange"
    >
      <l-tile-layer
        url="https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png"
        layer-type="base"
        name="OpenStreetMap"
      ></l-tile-layer>

      <!-- Render optimized layers -->
      <template v-for="layer in layers" :key="`geojson-${layer.id}`">
        <l-geo-json
          v-if="layer.filteredGeojson && layer.visible"
          :geojson="layer.filteredGeojson"
          :options="getLayerOptions(layer)"
          :options-style="styleFunction(layer)"
        />
      </template>
    </l-map>
  </div>
</template>

<script setup>
import { onMounted, ref, computed, watch, nextTick } from 'vue'
import 'leaflet/dist/leaflet.css'
import { latLng } from 'leaflet'
import L from 'leaflet'
import { LMap, LTileLayer, LGeoJson } from '@vue-leaflet/vue-leaflet'

// Import all geojson data from the assets/geojson folder
import bencanaRaw from '../assets/geojson/bencana.geojson?raw'
import bimaRaw from '../assets/geojson/bima.geojson?raw'
import hotspotRaw from '../assets/geojson/hotspot.geojson?raw'
import jalanProvinsiRaw from '../assets/geojson/jalan_provinsi.geojson?raw'
import jembatanRaw from '../assets/geojson/jembatan.geojson?raw'
import kondisiRaw from '../assets/geojson/kondisi.geojson?raw'
import lerengRaw from '../assets/geojson/lereng.geojson?raw'
import lokasiAlatRaw from '../assets/geojson/lokasi_alat.geojson?raw'

const zoom = ref(13) // Ensure zoom is a ref if v-model:zoom is used
const center = latLng(-7.0584606529532055, 109.0550994873047)
const mapBounds = ref(null)

// Performance settings
const PERFORMANCE_CONFIG = {
  // Maximum points to show at different zoom levels
  maxPointsByZoom: {
    1: 20,
    3: 50,
    6: 100,
    8: 200,
    10: 300,
    12: 600,
    14: 1200,
    16: 2500,
    18: 4000
  },
  // Clustering settings
  enableClustering: true,
  clusterRadius: 50,
  // Debounce delay for updates
  updateDelay: 150
}

const geojsonFileImports = [
  { name: 'Bencana', rawData: bencanaRaw, id: 'bencana' },
  { name: 'Bima', rawData: bimaRaw, id: 'bima' },
  { name: 'Hotspot', rawData: hotspotRaw, id: 'hotspot' },
  { name: 'Jalan Provinsi', rawData: jalanProvinsiRaw, id: 'jalan_provinsi' },
  { name: 'Jembatan', rawData: jembatanRaw, id: 'jembatan' },
  { name: 'Kondisi', rawData: kondisiRaw, id: 'kondisi' },
  { name: 'Lereng', rawData: lerengRaw, id: 'lereng' },
  { name: 'Lokasi Alat', rawData: lokasiAlatRaw, id: 'lokasi_alat' }
]

const layers = ref([])
const updateTimeout = ref(null)
const isUpdating = ref(false)

// Computed property for visible points count
const visiblePointsCount = computed(() => {
  return layers.value.reduce((total, layer) => {
    if (
      layer.visible &&
      layer.filteredGeojson &&
      layer.filteredGeojson.features
    ) {
      return total + layer.filteredGeojson.features.length
    }
    return total
  }, 0)
})

// Get maximum points allowed for current zoom level
const getMaxPointsForZoom = (zoomLevel) => {
  const config = PERFORMANCE_CONFIG.maxPointsByZoom
  const zoomLevels = Object.keys(config)
    .map(Number)
    .sort((a, b) => a - b)

  for (let i = zoomLevels.length - 1; i >= 0; i--) {
    if (zoomLevel >= zoomLevels[i]) {
      return config[zoomLevels[i]]
    }
  }
  return config[zoomLevels[0]]
}

// Create a deterministic hash from feature properties for consistent sampling
const getFeatureHash = (feature) => {
  const props = feature.properties || {}
  const coords = feature.geometry?.coordinates || [0, 0]
  const str = `${props.name || props.id || props.Nama || ''}${coords[0]}${coords[1]}`
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = (hash << 5) - hash + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return Math.abs(hash)
}

// Filter features based on zoom level and bounds
const filterFeaturesByZoomAndBounds = (features, zoomLevel, bounds) => {
  const maxPoints = getMaxPointsForZoom(zoomLevel)

  if (!features || features.length === 0) return []

  let filteredFeatures = features

  let inViewportFeatures = []
  let outsideViewportFeatures = []

  // Always separate features by viewport when bounds are available
  if (bounds) {
    features.forEach((feature) => {
      if (feature.geometry && feature.geometry.coordinates) {
        const [lng, lat] = feature.geometry.coordinates
        if (bounds.contains([lat, lng])) {
          inViewportFeatures.push(feature)
        } else {
          outsideViewportFeatures.push(feature)
        }
      }
    })

    console.log(
      `Viewport filtering: ${inViewportFeatures.length} in view, ${outsideViewportFeatures.length} outside`
    )

    // Prioritize showing ALL points in viewport if reasonable
    if (inViewportFeatures.length <= maxPoints * 2) {
      // If viewport has reasonable number of points, show them all
      filteredFeatures = inViewportFeatures
      console.log(`Showing all ${inViewportFeatures.length} points in viewport`)
    } else {
      // If too many points in viewport, apply sampling to viewport points
      const sortedInViewport = inViewportFeatures.sort((a, b) => {
        return getFeatureHash(a) - getFeatureHash(b)
      })

      // Use deterministic sampling for viewport points
      const viewportSampleRate = maxPoints / inViewportFeatures.length
      filteredFeatures = sortedInViewport.filter((feature) => {
        const hash = getFeatureHash(feature)
        return hash % 1000 < viewportSampleRate * 1000
      })

      // Ensure we don't exceed maxPoints
      if (filteredFeatures.length > maxPoints) {
        filteredFeatures = filteredFeatures.slice(0, maxPoints)
      }

      console.log(
        `Sampled ${filteredFeatures.length} from ${inViewportFeatures.length} viewport points`
      )
    }
  } else {
    // No bounds available, use global sampling
    if (filteredFeatures.length > maxPoints) {
      const sortedFeatures = filteredFeatures.sort((a, b) => {
        return getFeatureHash(a) - getFeatureHash(b)
      })

      const sampleRate = maxPoints / filteredFeatures.length
      filteredFeatures = sortedFeatures.filter((feature) => {
        const hash = getFeatureHash(feature)
        return hash % 1000 < sampleRate * 1000
      })

      if (filteredFeatures.length > maxPoints) {
        filteredFeatures = filteredFeatures.slice(0, maxPoints)
      }
    }
  }

  return filteredFeatures
}

const onEachFeatureOverlay = (feature, layer) => {
  layer.on('click', () => {})

  let popupContent = ''
  if (feature.properties.popUp) {
    popupContent += feature.properties.popUp
  } else {
    for (let k in feature.properties) {
      let v = feature.properties[k]
      if (v) {
        popupContent += `<b>${k} : </b>${v}<br />`
      }
    }
  }

  if (feature.properties && feature.properties.popupContent) {
    popupContent += feature.properties.popupContent
  }

  layer.bindPopup(popupContent)
}

const pointToLayer = (feature, latlng) => {
  // Check if the feature has a custom icon in properties
  if (feature.properties && feature.properties.icon) {
    return L.marker(latlng, {
      icon: L.icon({
        iconUrl: feature.properties.icon,
        iconSize: [30, 30], // Default size, can be customized
        iconAnchor: [15, 30], // Point of the icon which will correspond to marker's location
        popupAnchor: [0, -30] // Point from which the popup should open relative to the iconAnchor
      })
    })
  }
  // Return default marker if no custom icon
  return L.marker(latlng)
}

// Get layer-specific options
const getLayerOptions = (layer) => {
  return {
    interactive: true,
    onEachFeature: onEachFeatureOverlay,
    pointToLayer: pointToLayer
  }
}

// Update filtered geojson for all layers
const updateFilteredLayers = () => {
  // Prevent recursive calls
  if (isUpdating.value) {
    console.log('Update already in progress, skipping...')
    return
  }

  if (updateTimeout.value) {
    clearTimeout(updateTimeout.value)
  }

  updateTimeout.value = setTimeout(() => {
    isUpdating.value = true

    try {
      const currentZoom = zoom.value
      const bounds = mapBounds.value

      console.log(`Updating filtered layers for zoom ${currentZoom}`)

      layers.value.forEach((layer) => {
        if (layer.originalGeojson && layer.originalGeojson.features) {
          const originalCount = layer.originalGeojson.features.length
          const filteredFeatures = filterFeaturesByZoomAndBounds(
            layer.originalGeojson.features,
            currentZoom,
            bounds
          )

          console.log(
            `${layer.name}: ${originalCount} -> ${filteredFeatures.length} features at zoom ${currentZoom}`
          )

          layer.filteredGeojson = {
            type: 'FeatureCollection',
            features: filteredFeatures
          }
        }
      })
    } catch (error) {
      console.error('Error updating filtered layers:', error)
    } finally {
      isUpdating.value = false
    }
  }, PERFORMANCE_CONFIG.updateDelay)
}

// Event handlers
const onZoomChange = (newZoom) => {
  console.log(`Zoom changed from ${zoom.value} to ${newZoom}`)
  zoom.value = newZoom
  updateFilteredLayers()
}

const onBoundsChange = (newBounds) => {
  mapBounds.value = newBounds
  // Always update on bounds change since we now prioritize viewport filtering
  updateFilteredLayers()
}

const checkboxClicked = () => {
  window.scrollTo({
    top: 600,
    behavior: 'smooth'
  })
}

const styleFunction = (layer) => {
  // Return a function that takes feature and returns style object
  return (feature) => {
    // Style can be customized based on feature properties if needed
    return {
      weight: 2,
      color: '#3388ff',
      opacity: 1,
      fillColor: '#3388ff',
      fillOpacity: 0.2
    }
  }
}

onMounted(() => {
  geojsonFileImports.forEach((fileImport) => {
    try {
      const geojson = JSON.parse(fileImport.rawData)

      // Handle both array format and FeatureCollection format
      let originalGeojson
      if (Array.isArray(geojson)) {
        originalGeojson = { type: 'FeatureCollection', features: geojson }
      } else {
        originalGeojson = geojson
      }

      const totalFeatures = originalGeojson.features
        ? originalGeojson.features.length
        : 0

      console.log(`Loading ${fileImport.name}: ${totalFeatures} features`)

      const layer = {
        id: fileImport.id,
        name: fileImport.name,
        originalGeojson: originalGeojson,
        filteredGeojson: null, // Will be populated by updateFilteredLayers
        totalFeatures: totalFeatures,
        visible: ref(false) // Each layer gets its own reactive visibility state
      }

      layers.value.push(layer)
    } catch (error) {
      console.error(`Error parsing GeoJSON for ${fileImport.name}:`, error)
      layers.value.push({
        id: fileImport.id,
        name: `${fileImport.name} (Error loading)`,
        originalGeojson: null,
        filteredGeojson: null,
        totalFeatures: 0,
        visible: ref(false)
      })
    }
  })

  // Initial filter update
  nextTick(() => {
    updateFilteredLayers()
  })

  // Watch for visibility changes to trigger updates
  watch(
    () => layers.value.map((layer) => layer.visible),
    () => {
      updateFilteredLayers()
    },
    { deep: true }
  )

  // Watch for zoom changes to ensure filtering updates
  watch(
    () => zoom.value,
    (newZoom, oldZoom) => {
      console.log(`Zoom watcher: ${oldZoom} -> ${newZoom}`)
      updateFilteredLayers()
    }
  )
})
</script>

<style lang="scss">
.map-container {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.map {
  width: 100%;
  height: 100%;
}

.layer-control {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000; /* Ensure it's above the map */
  width: 250px;
  /* background-color: rgba(255, 255, 255, 0.8);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); */
}

/* Added for better layout of checkboxes if XCheckbox doesn't provide flex */
.flex.items-center {
  display: flex;
  align-items: center;
}
.gap-2 {
  gap: 0.5rem; /* 8px */
}
.mb-2 {
  margin-bottom: 0.5rem; /* 8px */
}
.mb-3 {
  margin-bottom: 0.75rem; /* 12px */
}
.ml-1 {
  margin-left: 0.25rem; /* 4px */
}
.text-sm {
  font-size: 0.875rem; /* 14px */
}
.text-xs {
  font-size: 0.75rem; /* 12px */
}
.text-gray-600 {
  color: #6b7280;
}
.text-gray-500 {
  color: #9ca3af;
}

.leaflet-popup-content {
  table {
    thead {
      th:first-child {
        width: 70px;
      }
      th {
        background-color: #f8f9fa;
        color: #212529;
        font-weight: bold;
        padding: 5px;
        vertical-align: top;
      }
    }
    tbody {
      tr {
        td:first-child {
          width: 70px;
        }
        td,
        th {
          padding: 5px;
          vertical-align: top;
        }
      }
    }
  }
}
</style>
