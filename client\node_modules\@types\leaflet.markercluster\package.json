{"name": "@types/leaflet.markercluster", "version": "1.5.5", "description": "TypeScript definitions for leaflet.markercluster", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet.markercluster", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON>ig", "url": "https://github.com/rimig"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nena<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet.markercluster"}, "scripts": {}, "dependencies": {"@types/leaflet": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "f63cedd1aefa6d0582dfd207cc507c684fb68a09efed191d250e88eb2f0f8097", "typeScriptVersion": "4.8"}