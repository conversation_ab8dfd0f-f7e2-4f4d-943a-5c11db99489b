<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">AddMarker</button><button id="doit2">Add by Timer</button><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/114">#114</a>. Markers are added to the map periodically using addLayers. Bug was that after becoming a cluster (size 2 or 3 usually) they would never change again even if more markers were added to them.</span><br/>

	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(40.782982, -73.969452);

		var map = new L.Map('map', { 
			center: latlng,
			zoom:    12,
			maxZoom: 12,
			layers: [tiles]
		});

		var markerCluster = new L.MarkerClusterGroup();
		map.addLayer(markerCluster);
		
		function getRandomLatLng() {
			return [
				 40.782982 + (Math.random() > 0.5 ? 0.5 : -0.5) * Math.random(),
				-73.969452 + (Math.random() > 0.5 ? 0.5 : -0.5) * Math.random()
			];
		}

		document.getElementById('doit').onclick = function () {
			markerCluster.addLayers([new L.Marker(map.getCenter())]);
		};
		document.getElementById('doit2').onclick = function () {
			setInterval(function () {
				var n = 100;
				var markers = new Array();

				for (var i = 0; i < n; i++) {
					markers.push(L.marker(getRandomLatLng()));
				}

				markerCluster.addLayers(markers);

			}, 1000);
		};

	</script>
</body>
</html>
