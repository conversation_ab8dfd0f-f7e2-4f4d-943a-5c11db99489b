<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="../screen.css" />

	<link rel="stylesheet" href="../../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../../dist/MarkerCluster.Default.css" />
	<script src="../../src/DistanceGrid.js"></script>
	<script src="../../src/MarkerCluster.js"></script>
	<script src="../../src/MarkerClusterGroup.js"></script>
	<script src="../../src/MarkerCluster.QuickHull.js"></script>
	<script src="../../src/MarkerCluster.Spiderfier.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">open popup</button><br/>
	<span>Bug <a href="https://github.com/danzel/Leaflet.markercluster/issues/65">#65</a>. Click 2 then click the button. You should be scrolled to the marker, old behaviour would zoom you out.</span><br/>
	<span id="time"></span>
	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(50.5, 30.51);

		var map = new L.Map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = new L.MarkerClusterGroup();
		var markersList = [];

		var m = new L.Marker(new L.LatLng(50.507, 30.502));
		m.bindPopup('asdasd');
		markersList.push(m);
		markers.addLayer(m);


		m = new L.Marker(new L.LatLng(50.493, 30.518));
		markersList.push(m);
		markers.addLayer(m);

		m = new L.Marker(new L.LatLng(50.493, 30.518));
		markersList.push(m);
		markers.addLayer(m);


		map.addLayer(markers);

		L.DomUtil.get('doit').onclick = function () {
			markers.zoomToShowLayer(markersList[0], function () {
				markersList[0].openPopup();
			});
		};

	</script>
</body>
</html>
