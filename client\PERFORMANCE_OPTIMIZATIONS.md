# Map Performance Optimizations

This document describes the performance optimizations implemented in the MapComponent.vue to handle large GeoJSON datasets efficiently.

## Problem

The original implementation was loading and rendering all GeoJSON features at once, which caused performance issues when dealing with large datasets:

- `hotspot.geojson`: ~17,000 features
- `jembatan.geojson`: ~47,000 features  
- `bencana.geojson`: ~8,000 features

This resulted in slow page loading and poor user experience when these layers were enabled.

## Solutions Implemented

### 1. Zoom-Based Feature Filtering

Features are filtered based on the current zoom level to show only a manageable number of points:

- Zoom 1-4: Maximum 50 points
- Zoom 5-7: Maximum 100 points
- Zoom 8-9: Maximum 500 points
- Zoom 10-11: Maximum 1,000 points
- Zoom 12-14: Maximum 2,000 points
- Zoom 15-17: Maximum 5,000 points
- Zoom 18+: Maximum 10,000 points

### 2. Viewport-Based Filtering

At lower zoom levels (< 12), only features within the current map bounds are rendered, reducing the number of DOM elements.

### 3. Smart Sampling

When the number of features exceeds the zoom-level limit, a sampling strategy is used to distribute points evenly across the dataset rather than just taking the first N features.

### 4. Debounced Updates

Map updates are debounced with a 300ms delay to prevent excessive re-rendering during zoom and pan operations.

### 5. Lazy Loading

Layers are only processed and filtered when they become visible, reducing initial load time.

## Performance Monitoring

The layer control panel now shows:
- Current zoom level
- Number of visible points across all layers
- Total feature count for each layer

## Configuration

Performance settings can be adjusted in the `PERFORMANCE_CONFIG` object:

```javascript
const PERFORMANCE_CONFIG = {
  maxPointsByZoom: {
    1: 50,
    5: 100,
    8: 500,
    10: 1000,
    12: 2000,
    15: 5000,
    18: 10000
  },
  updateDelay: 300
}
```

## Usage

The optimizations are automatic and transparent to the user. Users will notice:

1. **Faster initial loading** - Only visible layers are processed
2. **Smooth zooming** - Appropriate number of points for each zoom level
3. **Responsive panning** - Debounced updates prevent lag
4. **Progressive detail** - More points appear as you zoom in

## Technical Details

### Data Structure

Each layer now maintains:
- `originalGeojson`: Complete dataset
- `filteredGeojson`: Currently visible subset
- `totalFeatures`: Count for UI display

### Event Handling

- `onZoomChange`: Triggers filtered update when zoom changes
- `onBoundsChange`: Triggers filtered update when viewport changes
- Visibility changes are watched reactively

### Memory Management

The optimization reduces memory usage by:
- Not creating DOM elements for filtered-out features
- Reusing filtered datasets between updates
- Clearing timeouts to prevent memory leaks

## Future Enhancements

Potential additional optimizations:
1. **Marker Clustering**: Group nearby points at low zoom levels
2. **Web Workers**: Move filtering logic to background thread
3. **Tile-based Loading**: Load features only for visible tiles
4. **Feature Simplification**: Reduce geometry complexity at low zoom
