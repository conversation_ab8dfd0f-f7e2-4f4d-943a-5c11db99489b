<!DOCTYPE html>
<html>
<head>
	<title>Leaflet debug page</title>

	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.0.3/dist/leaflet.css" integrity="sha512-07I2e+7D8p6he1SIM+1twR5TIrhUQn9+I6yjqD53JQjFiMf8EtC93ty0/5vJTZGF8aAocvHYNEDJajGdNx1IsQ==" crossorigin="" />
	<script src="https://unpkg.com/leaflet@1.0.3/dist/leaflet-src.js" integrity="sha512-WXoSHqw/t26DszhdMhOXOkI7qCiv5QWXhH9R7CgvgZMHz1ImlkVQ3uNsiQKu5wwbbxtPzFXd1hK4tzno2VqhpA==" crossorigin=""></script>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="screen.css" />

	<link rel="stylesheet" href="../dist/MarkerCluster.css" />
	<link rel="stylesheet" href="../dist/MarkerCluster.Default.css" />
	<script src="../dist/leaflet.markercluster-src.js"></script>
</head>
<body>

	<div id="map"></div>
	<button id="doit">Remove marker #1</button><button id="doit2">Remove marker #2</button><br/>
	<span>New Bug. Spiderfy the cluster then click the button #1. All markers disapear, but it should remain marker #2.</span><br/>
	<span>New Bug. Spiderfy the cluster then click the button #2. All markers disapear, but it should remain marker #1.</span><br/>
	
	<script type="text/javascript">

		var tiles = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
				maxZoom: 18,
				attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
			}),
			latlng = new L.LatLng(50.5, 30.51);

		var map = new L.Map('map', {center: latlng, zoom: 15, layers: [tiles]});

		var markers = new L.MarkerClusterGroup({ disableClusteringAtZoom: 19});
		var markersList = [];
		var m;

                geoJsonFeature = { "type": "FeatureCollection",
                                   "features": [ { "type": "Feature",
                                                   "geometry": {
                                                        "type": "Point",
                                                        "coordinates": [30.51, 50.5]
                                                    }
                                                  }
                                               ]
                                 };
		m = L.geoJson(geoJsonFeature, { 
                     pointToLayer: function (feature, latlng) {		
                                 return L.marker(latlng);
                               }
                    });
		markersList.push(m);	
		markers.addLayer(m);		
		
		
		geoJsonFeature = { "type": "FeatureCollection",
                                   "features": [ { "type": "Feature",
                                                   "geometry": {
                                                        "type": "Point",
                                                        "coordinates": [30.5101, 50.5]
                                                    }
                                                  }
                                               ]
                                 };
		m = L.geoJson(geoJsonFeature, { 
                     pointToLayer: function (feature, latlng) {		
                                 return L.marker(latlng);
                               }
                    });
		markersList.push(m);	
		markers.addLayer(m);				

		map.addLayer(markers);

		L.DomUtil.get('doit').onclick = function () {
			markers.removeLayer(markersList[0]);
		};
		
		L.DomUtil.get('doit2').onclick = function () {
			markers.removeLayer(markersList[1]);
		};

	</script>
</body>
</html>
